#!/usr/bin/env python3
"""
Install PDF optimization libraries for faster processing.

This script installs the required libraries for fast PDF text extraction:
- PyMuPDF (fitz) - Fastest PDF processing
- pdfplumber - Good balance of speed and accuracy  
- PyPDF2 - Lightweight fallback option
"""

import subprocess
import sys

def install_package(package_name, import_name=None):
    """Install a package using pip."""
    if import_name is None:
        import_name = package_name
    
    try:
        __import__(import_name)
        print(f"✅ {package_name} is already installed")
        return True
    except ImportError:
        print(f"📦 Installing {package_name}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
            print(f"✅ {package_name} installed successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package_name}: {e}")
            return False

def main():
    """Install all PDF optimization libraries."""
    print("🚀 Installing PDF optimization libraries...")
    print("=" * 50)
    
    packages = [
        ("PyMuPDF", "fitz"),  # Fastest
        ("pdfplumber", "pdfplumber"),  # Good balance
        ("PyPDF2", "PyPDF2"),  # Lightweight fallback
    ]
    
    success_count = 0
    for package_name, import_name in packages:
        if install_package(package_name, import_name):
            success_count += 1
    
    print("=" * 50)
    print(f"📊 Installation Summary: {success_count}/{len(packages)} packages installed")
    
    if success_count == len(packages):
        print("🎉 All PDF optimization libraries installed successfully!")
        print("⚡ Your PDF processing will now be much faster!")
    elif success_count > 0:
        print("⚠️ Some libraries installed. PDF processing will be partially optimized.")
    else:
        print("❌ No optimization libraries could be installed.")
        print("💡 PDF processing will use the slower UnstructuredPDFLoader fallback.")
    
    print("\n💡 Performance Tips:")
    print("   • PyMuPDF (fitz) is the fastest - prioritized first")
    print("   • pdfplumber provides good OCR for scanned PDFs")
    print("   • PyPDF2 is a lightweight fallback option")
    print("   • Parallel processing is now enabled for multiple PDFs")

if __name__ == "__main__":
    main()
