import hashlib
import time
import random

def generate_org_uid(org_name: str, country: str, plant_name: str) -> dict:
    """
    Generate unique organization UID and return it with plant name.

    Format: ORG_{COUNTRY_CODE}_{ORG_HASH}_{TIMESTAMP}
    Example: ORG_IN_A7B2C9_12345678

    Args:
        org_name: Organization name
        country: Country name
        plant_name: Power plant name

    Returns:
        Dict containing plant_name and org_uid
        Example: { "plant_name": "XYZ Plant", "org_uid": "ORG_IN_A7B2C9_12345678" }
    """
    # Get country code (first 2 letters, uppercase)
    country_code = country[:2].upper()

    # Create hash from organization name
    org_hash = hashlib.sha256(org_name.lower().encode()).hexdigest()[:6].upper()

    # Add timestamp for uniqueness
    timestamp = str(int(time.time()))[-8:]  # Last 8 digits

    # Form the UID
    org_uid = f"ORG_{country_code}_{org_hash}_{timestamp}"

    # Return result as dictionary
    return {
        "plant_name": plant_name,
        "org_uid": org_uid
    }
