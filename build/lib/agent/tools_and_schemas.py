from typing import List
from pydantic import BaseModel, Field


class SearchQueryList(BaseModel):
    query: List[str] = Field(
        description="A list of search queries to be used for web research."
    )
    rationale: str = Field(
        description="A brief explanation of why these queries are relevant to the research topic."
    )


class Reflection(BaseModel):
    is_sufficient: bool = Field(
        description="Whether the provided summaries are sufficient to answer the user's question."
    )
    knowledge_gap: str = Field(
        description="A description of what information is missing or needs clarification."
    )
    follow_up_queries: List[str] = Field(
        description="A list of follow-up queries to address the knowledge gap."
    )


class PowerPlantReflection(BaseModel):
    is_sufficient: bool = Field(
        description="Whether sufficient annual report information has been found."
    )
    knowledge_gap: str = Field(
        description="Description of what annual report information is missing."
    )
    follow_up_queries: List[str] = Field(
        description="Follow-up queries to find annual reports or holding company information."
    )
    search_phase: str = Field(
        description="Current search phase: 'direct_search' or 'holding_company_search'"
    )
    holding_company_name: str = Field(
        default="",
        description="Name of the holding company if identified, empty string otherwise."
    )
