from datetime import datetime


# Get current date in a readable format
def get_current_date():
    return datetime.now().strftime("%B %d, %Y")


query_writer_instructions = """Your goal is to generate sophisticated and diverse web search queries. These queries are intended for an advanced automated web research tool capable of analyzing complex results, following links, and synthesizing information.

Instructions:
- Always prefer a single search query, only add another query if the original question requests multiple aspects or elements and one query is not enough.
- Each query should focus on one specific aspect of the original question.
- Don't produce more than {number_queries} queries.
- Queries should be diverse, if the topic is broad, generate more than 1 query.
- Don't generate multiple similar queries, 1 is enough.
- Query should ensure that the most current information is gathered. The current date is {current_date}.

Format: 
- Format your response as a JSON object with ALL two of these exact keys:
   - "rationale": Brief explanation of why these queries are relevant
   - "query": A list of search queries

Example:

Topic: What revenue grew more last year apple stock or the number of people buying an iphone
```json
{{
    "rationale": "To answer this comparative growth question accurately, we need specific data points on Apple's stock performance and iPhone sales metrics. These queries target the precise financial information needed: company revenue trends, product-specific unit sales figures, and stock price movement over the same fiscal period for direct comparison.",
    "query": ["Apple total revenue growth fiscal year 2024", "iPhone unit sales growth fiscal year 2024", "Apple stock price growth fiscal year 2024"],
}}
```

Context: {research_topic}"""


web_searcher_instructions = """Conduct targeted Google Searches to gather the most recent, credible information on "{research_topic}" and synthesize it into a verifiable text artifact.

Instructions:
- Query should ensure that the most current information is gathered. The current date is {current_date}.
- Conduct multiple, diverse searches to gather comprehensive information.
- Consolidate key findings while meticulously tracking the source(s) for each specific piece of information.
- The output should be a well-written summary or report based on your search findings. 
- Only include the information found in the search results, don't make up any information.

Research Topic:
{research_topic}
"""

reflection_instructions = """You are an expert research assistant analyzing summaries about "{research_topic}".

Instructions:
- Identify knowledge gaps or areas that need deeper exploration and generate a follow-up query. (1 or multiple).
- If provided summaries are sufficient to answer the user's question, don't generate a follow-up query.
- If there is a knowledge gap, generate a follow-up query that would help expand your understanding.
- Focus on technical details, implementation specifics, or emerging trends that weren't fully covered.

Requirements:
- Ensure the follow-up query is self-contained and includes necessary context for web search.

Output Format:
- Format your response as a JSON object with these exact keys:
   - "is_sufficient": true or false
   - "knowledge_gap": Describe what information is missing or needs clarification
   - "follow_up_queries": Write a specific question to address this gap

Example:
```json
{{
    "is_sufficient": true, // or false
    "knowledge_gap": "The summary lacks information about performance metrics and benchmarks", // "" if is_sufficient is true
    "follow_up_queries": ["What are typical performance benchmarks and metrics used to evaluate [specific technology]?"] // [] if is_sufficient is true
}}
```

Reflect carefully on the Summaries to identify knowledge gaps and produce a follow-up query. Then, produce your output following this JSON format:

Summaries:
{summaries}
"""

answer_instructions = """Generate a high-quality answer to the user's question based on the provided summaries.

Instructions:
- The current date is {current_date}.
- You are the final step of a multi-step research process, don't mention that you are the final step.
- You have access to all the information gathered from the previous steps.
- You have access to the user's question.
- Generate a high-quality answer to the user's question based on the provided summaries and the user's question.
- Include the sources you used from the Summaries in the answer correctly, use markdown format (e.g. [apnews](https://vertexaisearch.cloud.google.com/id/1-0)). THIS IS A MUST.

User Context:
- {research_topic}

Summaries:
{summaries}"""

# Power Plant Annual Report Search Prompts

power_plant_query_instructions = """Your goal is to generate targeted web search queries to find the EXACT annual reports page for a specific power plant. Focus on finding the specific webpage where annual report PDFs are hosted.

Instructions:
- Find the EXACT page where annual report PDFs are downloadable
- Target official company websites with specific annual report sections
- Look for URLs like "company.com/investor-relations/annual-reports" or "company.com/InvestorRelations/AnnualReportInv"
- Avoid general company information pages
- The current date is {current_date}

Power Plant Name: {plant_name}

Format your response as a JSON object with these exact keys:
- "rationale": Brief explanation of the search strategy
- "query": A list of 2 targeted search queries

Example:
```json
{{
    "rationale": "Searching for the specific annual reports download page on the official company website",
    "query": ["{plant_name} site:seilenergy.com annual report download", "{plant_name} investor relations annual reports PDF"]
}}
```

Context: Find the exact annual reports page for {plant_name} power plant"""

holding_company_search_instructions = """Your goal is to identify the holding company or parent company that owns the specified power plant, then search for their annual reports.

Instructions:
- First identify the holding company or parent company of the power plant
- Then search for annual reports from that holding company
- Focus on official company websites and investor relations pages
- Look for sections like "Investor Relations", "Financial Statements", "Annual Reports"
- The current date is {current_date}

Power Plant Name: {plant_name}

Format your response as a JSON object with these exact keys:
- "rationale": Brief explanation of the search strategy for finding the holding company
- "query": A list of 2-3 search queries to identify the holding company and find their annual reports

Example:
```json
{{
    "rationale": "First identifying the parent/holding company of the power plant, then searching for their official annual reports and investor relations materials",
    "query": ["{plant_name} power plant holding company parent company", "{plant_name} power plant owner operator company", "who owns {plant_name} power plant annual report"]
}}
```

Context: Find the holding company for {plant_name} power plant and locate their annual reports"""

power_plant_web_search_instructions = """Conduct targeted Google Searches to find the PRIMARY annual reports page for "{plant_name}" power plant.

Instructions:
- The current date is {current_date}
- Find the SINGLE MAIN page where annual report PDFs are hosted
- Look for official company websites with URLs like:
  * company.com/investor-relations/
  * company.com/annual-reports/
  * company.com/investors/
  * company.com/financials/
- PRIORITIZE clean, direct URLs over encoded/redirect URLs
- Focus on pages that contain MULTIPLE years of annual report PDFs
- Look for pages with clear navigation to downloadable annual reports
- Identify the specific page URL that hosts the annual report PDF downloads
- Only include information found in search results, don't make up any information

Search Focus: {search_phase}
Power Plant: {plant_name}

Expected Output:
- Identify the ONE primary official website page for annual reports
- Provide the direct URL to the annual report download page (not redirect URLs)
- Confirm that multiple annual report PDFs are available for download on this page
- If this is a holding company search, identify the parent company name and their main annual reports page
- Focus on the actual page where users can download annual report PDFs

CRITICAL: Look for the actual page where annual reports are hosted, not just general company pages."""

power_plant_reflection_instructions = """You are analyzing search results for annual reports related to "{plant_name}" power plant.

Instructions:
- Determine if a PRIMARY annual report page has been found with downloadable PDFs
- If direct power plant annual reports weren't found, assess if we need to search for the holding company
- Focus on finding ONE main official annual report download page, not multiple scattered links
- Look for specific investor relations pages that host multiple annual report PDFs

Current Search Phase: {search_phase}
Power Plant: {plant_name}

Requirements:
- Mark as sufficient ONLY if a clear primary annual reports page with downloadable PDFs is found
- If this was a direct power plant search and no primary annual reports page was found, recommend searching for the holding company
- If this was a holding company search, determine if the main annual report page was found
- Focus on finding the MAIN page that hosts annual report PDFs, not just company information

Output Format:
```json
{{
    "is_sufficient": true/false,
    "knowledge_gap": "Description of what's missing - need primary annual reports page",
    "follow_up_queries": ["Specific follow-up queries if needed"],
    "search_phase": "direct_search" or "holding_company_search",
    "holding_company_name": "Name if identified, otherwise null"
}}
```

Search Results Summary:
{summaries}"""

power_plant_final_answer_instructions = """Generate a focused response for the power plant annual report search.

Instructions:
- The current date is {current_date}
- Provide ONLY the EXACT website URL where annual report PDFs can be downloaded
- Return the SINGLE BEST annual reports page, not multiple pages
- Focus on the specific page that hosts downloadable annual report PDFs
- Provide the direct, clean URL (like https://www.seilenergy.com/InvestorRelations/AnnualReportInv)
- Avoid third-party sites like tracxn, zaubacorp, careratings
- If a holding company was identified, mention it clearly

Power Plant: {plant_name}
Search Results: {summaries}

Expected Output Format:
**Power Plant:** [Name]
**Holding Company:** [Name if applicable]

**Primary Annual Report URL:**
* **[EXACT URL]**
  * Brief description of available annual reports

CRITICAL: Return only ONE primary URL where annual report PDFs are actually downloadable. Do not include multiple URLs or third-party sites."""
