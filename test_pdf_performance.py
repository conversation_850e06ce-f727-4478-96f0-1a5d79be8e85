#!/usr/bin/env python3
"""
Test PDF processing performance with different methods.

This script compares the performance of different PDF text extraction methods
to demonstrate the speed improvements.
"""

import os
import time
import sys
from pathlib import Path

# Add the agent directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'agent'))

def test_pdf_extraction_speed(pdf_path: str):
    """Test different PDF extraction methods and compare speeds."""
    if not os.path.exists(pdf_path):
        print(f"❌ PDF file not found: {pdf_path}")
        return
    
    print(f"🔍 Testing PDF extraction speed for: {os.path.basename(pdf_path)}")
    print("=" * 60)
    
    results = {}
    
    # Test PyMuPDF (fitz)
    try:
        import fitz
        start_time = time.time()
        doc = fitz.open(pdf_path)
        text = ""
        for page_num in range(min(3, len(doc))):
            page = doc.load_page(page_num)
            text += page.get_text()
        doc.close()
        end_time = time.time()
        results["PyMuPDF (fitz)"] = {
            "time": end_time - start_time,
            "text_length": len(text),
            "status": "✅ Success"
        }
    except ImportError:
        results["PyMuPDF (fitz)"] = {"status": "❌ Not installed"}
    except Exception as e:
        results["PyMuPDF (fitz)"] = {"status": f"❌ Error: {str(e)[:50]}"}
    
    # Test pdfplumber
    try:
        import pdfplumber
        start_time = time.time()
        text = ""
        with pdfplumber.open(pdf_path) as pdf:
            for page_num in range(min(3, len(pdf.pages))):
                page = pdf.pages[page_num]
                page_text = page.extract_text()
                if page_text:
                    text += page_text
        end_time = time.time()
        results["pdfplumber"] = {
            "time": end_time - start_time,
            "text_length": len(text),
            "status": "✅ Success"
        }
    except ImportError:
        results["pdfplumber"] = {"status": "❌ Not installed"}
    except Exception as e:
        results["pdfplumber"] = {"status": f"❌ Error: {str(e)[:50]}"}
    
    # Test PyPDF2
    try:
        import PyPDF2
        start_time = time.time()
        text = ""
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page_num in range(min(3, len(pdf_reader.pages))):
                page = pdf_reader.pages[page_num]
                text += page.extract_text()
        end_time = time.time()
        results["PyPDF2"] = {
            "time": end_time - start_time,
            "text_length": len(text),
            "status": "✅ Success"
        }
    except ImportError:
        results["PyPDF2"] = {"status": "❌ Not installed"}
    except Exception as e:
        results["PyPDF2"] = {"status": f"❌ Error: {str(e)[:50]}"}
    
    # Test UnstructuredPDFLoader (original slow method)
    try:
        from langchain_community.document_loaders import UnstructuredPDFLoader
        start_time = time.time()
        loader = UnstructuredPDFLoader(
            file_path=pdf_path,
            strategy="fast",
            languages=["eng"]
        )
        documents = loader.load()
        text = ""
        for i, doc in enumerate(documents):
            if i >= 3:
                break
            text += doc.page_content
        end_time = time.time()
        results["UnstructuredPDFLoader"] = {
            "time": end_time - start_time,
            "text_length": len(text),
            "status": "✅ Success"
        }
    except ImportError:
        results["UnstructuredPDFLoader"] = {"status": "❌ Not installed"}
    except Exception as e:
        results["UnstructuredPDFLoader"] = {"status": f"❌ Error: {str(e)[:50]}"}
    
    # Print results
    print("📊 Performance Results:")
    print("-" * 60)
    
    successful_results = {k: v for k, v in results.items() if "time" in v}
    
    if successful_results:
        # Sort by speed (fastest first)
        sorted_results = sorted(successful_results.items(), key=lambda x: x[1]["time"])
        
        fastest_time = sorted_results[0][1]["time"]
        
        for method, data in sorted_results:
            speedup = fastest_time / data["time"] if data["time"] > 0 else 1
            print(f"{method:20} | {data['time']:.3f}s | {data['text_length']:,} chars | {speedup:.1f}x speed")
        
        print("-" * 60)
        print(f"🏆 Fastest: {sorted_results[0][0]} ({sorted_results[0][1]['time']:.3f}s)")
        if len(sorted_results) > 1:
            slowest = sorted_results[-1]
            improvement = slowest[1]["time"] / fastest_time
            print(f"⚡ Speed improvement: {improvement:.1f}x faster than {slowest[0]}")
    
    # Print failed methods
    failed_results = {k: v for k, v in results.items() if "time" not in v}
    if failed_results:
        print("\n❌ Failed Methods:")
        for method, data in failed_results.items():
            print(f"   {method}: {data['status']}")

def find_sample_pdf():
    """Find a sample PDF file for testing."""
    # Look for PDFs in downloads directory
    downloads_dir = Path("./downloads")
    if downloads_dir.exists():
        for pdf_file in downloads_dir.rglob("*.pdf"):
            return str(pdf_file)
    
    # Look for PDFs in current directory
    for pdf_file in Path(".").glob("*.pdf"):
        return str(pdf_file)
    
    return None

def main():
    """Main function to run performance tests."""
    print("🚀 PDF Processing Performance Test")
    print("=" * 60)
    
    # Check if a specific PDF was provided
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
    else:
        pdf_path = find_sample_pdf()
    
    if not pdf_path:
        print("❌ No PDF file found for testing.")
        print("💡 Usage: python test_pdf_performance.py [path_to_pdf]")
        print("💡 Or place a PDF file in the current directory or ./downloads/")
        return
    
    test_pdf_extraction_speed(pdf_path)
    
    print("\n💡 Recommendations:")
    print("   1. Install PyMuPDF for fastest processing: pip install PyMuPDF")
    print("   2. Install pdfplumber for good OCR support: pip install pdfplumber")
    print("   3. Use parallel processing for multiple PDFs")
    print("   4. Reduce max_pages parameter for faster classification")

if __name__ == "__main__":
    main()
