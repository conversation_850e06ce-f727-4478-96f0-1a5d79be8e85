#!/usr/bin/env python3
"""
Power Plant Annual Report Search Function

This module provides a function to search for and download annual reports 
for power plants using comprehensive automated mode only.
"""

import os
import sys
import logging
from typing import Dict, Any, List

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# Add the src directory to the path so we can import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from agent.comprehensive_automated_scraper import ComprehensiveAutomatedScraper


def search_power_plant_reports(plant_name: str) -> Dict[str, Any]:
    """
    Search for and download annual reports for a power plant using comprehensive automated mode.
    
    Args:
        plant_name: Name of the power plant to search for
        
    Returns:
        Dictionary containing:
        - success: Boolean indicating if the search was successful
        - downloaded_files: List of downloaded file paths
        - error: Error message if failed
        - plant_name: The input plant name
    """
    
    # Check for required environment variables
    if not os.getenv("GEMINI_API_KEY"):
        error_msg = "GEMINI_API_KEY environment variable is not set. Please set your Gemini API key."
        logger.error(error_msg)
        return {
            "success": False,
            "error": error_msg,
            "downloaded_files": [],
            "plant_name": plant_name
        }
    
    if not os.getenv("OPENAI_API_KEY"):
        error_msg = "OPENAI_API_KEY environment variable is not set. Please set your OpenAI API key."
        logger.error(error_msg)
        return {
            "success": False,
            "error": error_msg,
            "downloaded_files": [],
            "plant_name": plant_name
        }
    
    # Check for ScraperAPI key (warn but continue)
    if not os.getenv("SCRAPER_API_KEY"):
        logger.warning("SCRAPER_API_KEY environment variable is not set.")
        logger.warning("PDF scraping functionality will be limited without a ScraperAPI key.")
        logger.warning("To enable full PDF scraping, set your ScraperAPI key:")
        logger.warning("Example: export SCRAPER_API_KEY='your-api-key-here'")
    
    logger.info(f"Using Comprehensive Automated Mode for: {plant_name}")
    logger.info("Fully automated search with:")
    logger.info("   - AI-powered search and analysis")
    logger.info("   - Proper fallback: ScraperAPI → Playwright → Selenium → Direct")
    logger.info("   - Auto-classification with OpenAI")
    logger.info("   - Auto-deletion of non-consolidated reports")
    
    try:
        comprehensive_scraper = ComprehensiveAutomatedScraper(download_dir="./downloads")
        results = comprehensive_scraper.run_comprehensive_automation(plant_name)
        
        if results["success"]:
            logger.info("Automated process completed successfully!")
            downloaded_files = results.get("downloaded_files", [])
            
            if downloaded_files:
                logger.info(f"Successfully downloaded {len(downloaded_files)} PDF(s):")
                for file_path in downloaded_files:
                    logger.info(f"   📄 {file_path}")
                logger.info(f"Files saved in: ./downloads/{plant_name.replace(' ', '_')}/")
            else:
                logger.info("Process completed but no PDF files were downloaded.")
                logger.info("This could mean:")
                logger.info("   - No annual report PDFs were found for this power plant")
                logger.info("   - The power plant name might need to be more specific")
                logger.info("   - The reports might not be publicly available")
            
            return {
                "success": True,
                "downloaded_files": downloaded_files,
                "error": None,
                "plant_name": plant_name
            }
        else:
            error_msg = results.get('error', 'Unknown error occurred during comprehensive search')
            logger.error(f"Automated process failed: {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "downloaded_files": [],
                "plant_name": plant_name
            }
                
    except Exception as e:
        error_msg = str(e)
        logger.error(f"Unexpected error during comprehensive search: {error_msg}")
        
        # Handle specific Google Generative AI client errors
        if "proxies" in error_msg or "unexpected keyword argument" in error_msg:
            logger.warning("This appears to be a Google Generative AI client version compatibility issue.")
            logger.warning("Try updating your google-genai package:")
            logger.warning("   pip install --upgrade google-genai")
            logger.warning("Or check your GEMINI_API_KEY configuration.")
            error_msg = f"Google Generative AI client error: {error_msg}"
        elif "API key" in error_msg.lower():
            logger.warning("This appears to be an API key configuration issue.")
            logger.warning("Please check your environment variables:")
            logger.warning("   - GEMINI_API_KEY")
            logger.warning("   - OPENAI_API_KEY")
            logger.warning("   - SCRAPER_API_KEY (optional but recommended)")
        
        return {
            "success": False,
            "error": error_msg,
            "downloaded_files": [],
            "plant_name": plant_name
        }


def batch_search_power_plants(plant_names: List[str]) -> Dict[str, Dict[str, Any]]:
    """
    Search for annual reports for multiple power plants.
    
    Args:
        plant_names: List of power plant names to search for
        
    Returns:
        Dictionary with plant names as keys and search results as values
    """
    results = {}
    
    logger.info(f"Starting batch search for {len(plant_names)} power plants...")
    logger.info("=" * 60)
    
    for i, plant_name in enumerate(plant_names, 1):
        logger.info(f"Processing {i}/{len(plant_names)}: {plant_name}")
        logger.info("-" * 50)
        
        result = search_power_plant_reports(plant_name)
        results[plant_name] = result
        
        if result["success"]:
            file_count = len(result["downloaded_files"])
            logger.info(f"{plant_name}: {file_count} files downloaded")
        else:
            logger.error(f"{plant_name}: {result['error']}")
        
        logger.info("-" * 50)
    
    # Summary
    logger.info("BATCH SEARCH SUMMARY")
    logger.info("=" * 60)
    
    successful = sum(1 for r in results.values() if r["success"])
    total_files = sum(len(r["downloaded_files"]) for r in results.values())
    
    logger.info(f"Successful searches: {successful}/{len(plant_names)}")
    logger.info(f"Total files downloaded: {total_files}")
    
    if successful < len(plant_names):
        logger.info("Failed searches:")
        for name, result in results.items():
            if not result["success"]:
                logger.error(f"   - {name}: {result['error']}")
    
    return results

import os

def has_annual_report(downloads_path="downloads"):
    """
    Checks if the specified downloads folder has any files.

    Args:
        downloads_path (str): Path to the downloads folder. Defaults to "downloads".

    Returns:
        bool: True if the folder has any files, False otherwise.
    """
    if not os.path.exists(downloads_path):
        return False

    return any(os.path.isfile(os.path.join(downloads_path, f)) for f in os.listdir(downloads_path))



import shutil
from pathlib import Path

def move_pdfs_to_pipeline_downloads(source_dir: str, pipeline_download_dir: str = "downloads"):
    """
    Move all PDF files from source_dir to pipeline's downloads/ folder.
    Creates the folder if it doesn't exist.
    """
    source = Path(source_dir)
    target = Path(pipeline_download_dir)
    target.mkdir(parents=True, exist_ok=True)

    for file in source.glob("*.pdf"):
        target_path = target / file.name
        if file.resolve() != target_path.resolve():
            shutil.move(str(file), str(target_path))
            print(f"✅ Moved {file.name} → {target_path}")
        else:
            print(f"⚠️ Skipped (already in place): {file.name}")











# # Example usage
# if __name__ == "__main__":
#     # Single plant search
#     plant_name = "Bhilai Renewable Energy Plant"
    
#     logger.info("Power Plant Annual Report Search - Comprehensive Mode Only")
#     logger.info("=" * 70)
    
#     results = search_power_plant_reports(plant_name)
    
#     if results["success"]:
#         logger.info(f"Search completed successfully for: {plant_name}")
#         if results["downloaded_files"]:
#             logger.info(f"Downloaded {len(results['downloaded_files'])} files:")
#             for file_path in results["downloaded_files"]:
#                 logger.info(f"   - {os.path.basename(file_path)}")
#         else:
#             logger.info("No files were downloaded (see details above)")
#     else:
#         logger.error(f"Search failed for: {plant_name}")
#         logger.error(f"Error: {results['error']}")
    
#     logger.info("=" * 70)
    
#     # Example of batch search (uncomment to test)
#     """
#     plant_list = [
#         "Bhilai Renewable Energy Plant",
#         "Adani Solar Power Plant",
#         "NTPC Thermal Power Station"
#     ]
    
#     batch_results = batch_search_power_plants(plant_list)
#     """