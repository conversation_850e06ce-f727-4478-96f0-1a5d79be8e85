# To Run the FastAPI 
uvicorn main:app --reload

# To Test with curl command 
curl -X POST http://127.0.0.1:8000/run-pipeline \
  -H "Content-Type: application/json" \
  -d '{"plantName": "Cirebon Power Plant", "Product": "clem-transition"}'


{
  "status": "success",
  "message": "Pipeline triggered for Cirebon Power Plant",
  "org_name": "Cirebon Electric Power",
  "country": "Indonesia",
  "uid": "ORG_IDN_ABC123_12345678"
}
