import requests
from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel
from typing import Dict

from get_org_country import get_power_plant_details
from get_org_id import generate_org_uid
from power_plant_search_function import has_annual_report,run_downloader

app = FastAPI()

class PipelineRequest(BaseModel):
    plantName: str
    Product: str

@app.post("/run-pipeline")
def run_pipeline(input_data: PipelineRequest) -> Dict:
    plant_name = input_data.plantName
    domain_name = input_data.Product

    if not plant_name or not domain_name:
        raise HTTPException(status_code=400, detail="Both 'plantName' and 'Product' are required.")

    try:
        # run_downloader(plant_name, from_year=2020, to_year=2025)
        annual_report = has_annual_report()
        if annual_report:
            org_details = get_power_plant_details(plant_name)
            org_name = org_details.get("org_name")
            country = org_details.get("country")
            uid_info = generate_org_uid(org_name, country, plant_name)

            # Main pipeline logic
            if domain_name == "clem-transition":

                # 🔁 Call First Pipeline API (local)
                first_api_url = "http://127.0.0.1:8000/api/process/clem_trans_financial"
                first_api_payload = {
                    "plant_name": plant_name,
                    "country_name": country,
                    "entity_id": uid_info.get("org_uid")
                }
                first_response = requests.post(first_api_url, data=first_api_payload)
                print(f"First API response: {first_response.status_code} - {first_response.text}")

                # 🔁 Call Second Pipeline API (public tunnel)
                # 🔁 Call Second Pipeline API (public tunnel)
                second_api_url = "https://slxpzl4b-8000.inc1.devtunnels.ms/api/v1/extraction/single-plant"
                second_api_payload = {
                    "plant_name": plant_name
                }
                print("Calling Second API with payload:", second_api_payload)
                try:
                    second_response = requests.post(
                        second_api_url,
                        json=second_api_payload,
                        timeout=10
                    )
                    print("Second API status code:", second_response.status_code)
                    print("Second API response:", second_response.text)
                except requests.exceptions.RequestException as e:
                    print("Second API request failed:", str(e))


            return {
                "status": "success",
                "message": f"Pipeline triggered for {plant_name}",
                "org_name": org_name,
                "country": country,
                "uid": uid_info.get("org_uid")
            }
        else:
            return {
                "status": "skipped",
                "message": f"No annual report found for {plant_name}"
            }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
