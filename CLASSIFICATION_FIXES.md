# PDF Classification & Download Structure Fixes

## Issues Fixed

### 1. 🚨 **Classification Problem - Valid Reports Being Deleted**

**Root Cause**: The OpenAI classification was too strict and couldn't handle:
- Plant name variations (AMARAVATI vs Amravati)
- Company vs Plant relationships (RattanIndia Power Limited operates Amravati TPP)
- Notice documents vs full annual reports
- Spelling differences and case sensitivity

**Example Issue**:
```
Search: "AMARAVATI TPP"
PDF Content: "RattanIndia Power Limited" + "Amravati TPP"
Result: "Belongs to plant: NO" → PDF DELETED ❌
```

### 2. 📁 **Download Structure Problem**

**Root Cause**: PDFs were being saved in plant-specific subfolders:
```
downloads/
  └── AMARAVATI_TPP/
      ├── AR-2017-18-RPL.pdf
      ├── Notice_AR_RPL_2023-24.pdf
      └── ...
```

**User Request**: Save all PDFs directly in downloads directory:
```
downloads/
  ├── AR-2017-18-RPL.pdf
  ├── Notice_AR_RPL_2023-24.pdf
  └── ...
```

## Solutions Implemented

### 🔧 **1. Enhanced Plant Name Matching**

**Added `_generate_plant_name_variations()` method**:
```python
def _generate_plant_name_variations(self, plant_name: str) -> list:
    variations = set()
    
    # Add original name
    variations.add(plant_name.lower())
    
    # Add individual words
    words = [word for word in plant_name.lower().split() if len(word) > 2]
    variations.update(words)
    
    # Add specific variations for AMARAVATI TPP
    if 'amaravati' in plant_name.lower():
        variations.update([
            'amaravati', 'amravati', 'amaravati tpp', 'amravati tpp',
            'rattanindia', 'rattan india', 'rattanindia power', 'rpl'
        ])
    
    return list(variations)
```

**Result**: Now matches:
- AMARAVATI TPP ✅
- Amravati TPP ✅  
- RattanIndia Power Limited ✅
- RPL ✅

### 🔧 **2. More Conservative Classification Logic**

**Before** (Too Strict):
```python
if not (is_annual and is_correct_plant):
    return False  # Delete if either condition fails
```

**After** (Conservative):
```python
if not is_annual:
    return False  # Only delete if clearly not an annual report

if not is_correct_plant:
    print("⚠️ Plant match unclear, but keeping annual report to be safe")
    # Don't delete - be conservative!

# Only delete consolidated-only if we're certain about plant match
if has_consolidated and not has_standalone:
    if is_correct_plant:
        return False  # Delete consolidated-only
    else:
        return True   # Keep if uncertain about plant match
```

### 🔧 **3. Enhanced OpenAI Prompts**

**Added plant name variations to prompts**:
```python
variations_text = ", ".join(plant_variations[:5])
user_prompt = f"""
IMPORTANT: The plant name may appear as variations like: {variations_text}
Also look for company names that operate this plant 
(e.g., RattanIndia Power Limited operates Amravati TPP).

Questions:
1. Annual report: Is this an annual report, notice, or financial document?
2. Belongs to plant: Does this belong to "{plant_name}" or its operating company?
...
```

### 🔧 **4. Fixed Download Directory Structure**

**Updated all scrapers**:
- `ScraperAPIPDFScraper` ✅
- `UniversalPDFDownloader` ✅  
- `PDFScraper` (Selenium) ✅

**Before**:
```python
plant_dir = os.path.join(self.download_dir, plant_name.replace(' ', '_'))
file_path = os.path.join(plant_dir, filename)
```

**After**:
```python
# Save directly in downloads directory
file_path = os.path.join(self.download_dir, filename)
```

## Expected Results

### ✅ **Classification Improvements**

1. **Notice_AR_RPL_2023-24.pdf** - Will now be KEPT instead of deleted
2. **Better plant matching** - Handles AMARAVATI vs Amravati vs RattanIndia Power
3. **Conservative approach** - Keeps uncertain cases rather than deleting them
4. **Enhanced fallback** - Better keyword-based classification when OpenAI fails

### ✅ **Download Structure**

All PDFs now saved directly in `./downloads/` directory:
```
downloads/
├── AR-2017-18-RPL.pdf
├── Notice_AR_RPL_2023-24.pdf
├── AnnualReportRPL.pdf
├── RPL-AR-2016-172.pdf
└── ...
```

## Testing

To test the fixes:

1. **Run the scraper**:
```bash
python -c "
from agent.comprehensive_automated_scraper import ComprehensiveAutomatedScraper
scraper = ComprehensiveAutomatedScraper()
results = scraper.run_comprehensive_automation('AMARAVATI TPP')
"
```

2. **Check results**:
- More PDFs should be kept (especially Notice documents)
- All PDFs should be in `./downloads/` (not subfolders)
- Classification should be more conservative

3. **Verify classification**:
- Look for "⚠️ Plant match unclear, but keeping annual report to be safe"
- Should see fewer "🗑️ DELETE" messages
- More "✅ KEEP" messages

## Key Benefits

1. **Fewer False Deletions** - Valid annual reports won't be deleted
2. **Better Plant Matching** - Handles name variations and company relationships
3. **Simpler File Organization** - All PDFs in one directory
4. **Conservative Approach** - When in doubt, keep the file
5. **Enhanced Debugging** - Better logging to understand classification decisions

The fixes ensure that valid annual reports like `Notice_AR_RPL_2023-24.pdf` are properly identified and kept, while organizing all downloaded PDFs in a single, easy-to-access directory.
