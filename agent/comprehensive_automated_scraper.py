#!/usr/bin/env python3
"""
Comprehensive Automated Power Plant Annual Report Scraper

This module provides a fully automated scraping system that:
1. Uses generic agent-based search (no hardcoding)
2. Implements proper fallback order: ScraperAPI → Playwright → Selenium → Direct
3. Automatically scrapes PDFs without permission prompts
4. Uses OpenAI to classify and delete non-consolidated annual reports
"""

import os
import sys
import time
import concurrent.futures
from typing import List, Dict, Any, Optional
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables FIRST
load_dotenv()

# Import OpenAI BEFORE any other modules that might interfere
from openai import OpenAI

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Core imports
from agent.graph import power_plant_graph
from agent.configuration import Configuration
from agent.scraper_api_pdf_scraper import ScraperAPIPDFScraper
from agent.pdf_scraper import PDFScraper
from agent.universal_pdf_downloader import download_pdfs_advanced
from agent.content_analyzer import ContentAnalyzer
try:
    from langchain_community.document_loaders import UnstructuredPDFLoader
except ImportError:
    try:
        from langchain.document_loaders import UnstructuredPDFLoader
    except ImportError:
        print("⚠️ Warning: LangChain PDF loader not available. PDF text extraction may be limited.")

# Fast PDF text extraction alternatives
try:
    import PyPDF2
    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False

try:
    import pdfplumber
    PDFPLUMBER_AVAILABLE = True
except ImportError:
    PDFPLUMBER_AVAILABLE = False

try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False

# Environment variables already loaded above


class ComprehensiveAutomatedScraper:
    """Fully automated scraper with proper fallback order and OpenAI classification."""
    
    def __init__(self, download_dir: str = "./downloads"):
        """Initialize the comprehensive scraper.
        
        Args:
            download_dir: Directory to save downloaded PDFs
        """
        self.download_dir = download_dir
        self.content_analyzer = ContentAnalyzer()
        
        # Initialize scrapers in fallback order
        self.scraper_api = ScraperAPIPDFScraper(download_dir=download_dir)
        # Selenium scraper will be initialized when needed
        self.selenium_scraper = None
        
        # Initialize OpenAI client for PDF validation
        self.openai_client = self._initialize_openai_client()

        # Initialize statistics and download directory
        self._initialize_stats()

    def _initialize_openai_client(self):
        """Initialize OpenAI client with robust error handling"""
        api_key = os.getenv("OPENAI_API_KEY")

        if not api_key:
            print("⚠️ OPENAI_API_KEY not found in environment variables")
            return None

        # Strategy 1: Standard initialization
        try:
            client = OpenAI(api_key=api_key)
            # Test the client with a simple call
            client.models.list()
            print("✅ OpenAI client initialized successfully")
            return client
        except Exception as e:
            print(f"⚠️ Standard OpenAI initialization failed: {e}")

        # Strategy 2: Minimal initialization
        try:
            client = OpenAI()
            client.api_key = api_key
            # Test the client
            client.models.list()
            print("✅ OpenAI client initialized with fallback method")
            return client
        except Exception as e:
            print(f"⚠️ Fallback OpenAI initialization failed: {e}")

        # Strategy 3: Environment variable approach
        try:
            os.environ["OPENAI_API_KEY"] = api_key
            client = OpenAI()
            # Test the client
            client.models.list()
            print("✅ OpenAI client initialized with environment variable")
            return client
        except Exception as e:
            print(f"⚠️ Environment variable OpenAI initialization failed: {e}")

        print("❌ All OpenAI initialization strategies failed. Using fallback classification.")
        return None

    def _initialize_stats(self):
        """Initialize statistics tracking"""
        # Create download directory
        os.makedirs(self.download_dir, exist_ok=True)

        # Statistics
        self.stats = {
            "scraperapi_success": 0,
            "playwright_success": 0,
            "selenium_success": 0,
            "direct_success": 0,
            "total_pdfs_downloaded": 0,
            "valid_consolidated_reports": 0,
            "deleted_non_consolidated": 0
        }

    def _enhanced_fallback_classification(self, text: str, plant_name: str) -> bool:
        """Enhanced fallback classification when OpenAI is not available.

        Uses keyword analysis and heuristics to determine if a PDF should be kept.

        Args:
            text: Extracted text from PDF
            plant_name: Name of the power plant

        Returns:
            True if PDF should be kept, False if it should be deleted
        """
        text_lower = text.lower()
        plant_lower = plant_name.lower()

        # Extract key words from plant name for matching
        plant_words = [word.strip() for word in plant_lower.replace(',', ' ').split() if len(word.strip()) > 2]

        # Check for annual report indicators
        annual_indicators = [
            'annual report', 'yearly report', 'annual financial', 'financial report',
            'annual statement', 'consolidated annual', 'standalone annual',
            'form 10-k', 'form 20-f', 'sec filing', 'investor relations'
        ]

        has_annual_indicator = any(indicator in text_lower for indicator in annual_indicators)

        # Check for plant/company name match
        plant_match_score = sum(1 for word in plant_words if word in text_lower)
        has_plant_match = plant_match_score >= max(1, len(plant_words) // 2)  # At least half the words match

        # Check for consolidated vs standalone indicators
        consolidated_indicators = ['consolidated', 'group', 'holding', 'parent company']
        standalone_indicators = ['standalone', 'separate', 'individual', 'subsidiary']

        has_consolidated = any(indicator in text_lower for indicator in consolidated_indicators)
        has_standalone = any(indicator in text_lower for indicator in standalone_indicators)

        # Decision logic (same as OpenAI version)
        if has_annual_indicator and has_plant_match:
            if has_standalone and has_consolidated:
                print("✅ KEEP: Contains both standalone and consolidated (enhanced fallback)")
                return True
            elif has_standalone and not has_consolidated:
                print("✅ KEEP: Contains standalone reports only (enhanced fallback)")
                return True
            elif has_consolidated and not has_standalone:
                print("🗑️ DELETE: Contains only consolidated reports (enhanced fallback)")
                return False
            else:
                # No clear indication, but it's an annual report for the right company
                print("✅ KEEP: Annual report for correct company, type unclear (enhanced fallback)")
                return True
        elif has_annual_indicator:
            print("⚠️ KEEP: Annual report but company match unclear (enhanced fallback)")
            return True  # Conservative approach - keep if it's an annual report
        elif has_plant_match:
            print("⚠️ KEEP: Company match but document type unclear (enhanced fallback)")
            return True  # Conservative approach - keep if it matches the company
        else:
            print("🗑️ DELETE: No clear annual report or company indicators (enhanced fallback)")
            return False

    def generic_search_for_annual_reports(self, plant_name: str) -> Dict[str, Any]:
        """Generic agent-based search for annual reports (no hardcoding).
        
        Args:
            plant_name: Name of the power plant
            
        Returns:
            Dictionary containing search results and sources
        """
        print(f"🔍 Starting generic search for: {plant_name}")
        print("⏳ Using AI agents to discover annual report sources...")
        
        # Initialize state for generic search
        initial_state = {
            "messages": [],
            "plant_name": plant_name,
            "search_phase": "direct_search",
            "search_query": [],
            "web_research_result": [],
            "sources_gathered": [],
            "annual_report_urls": [],
            "initial_search_query_count": 2,
            "max_research_loops": 2,
            "research_loop_count": 0,
            "reasoning_model": "gemini-2.5-flash",
            "holding_company_name": "",
            "found_annual_reports": False,
        }
        
        try:
            # Run the power plant search graph
            final_state = power_plant_graph.invoke(initial_state)

            if not final_state or not final_state.get("sources_gathered"):
                print("⚠️ Generic search found no sources, trying direct search...")
                return self._fallback_direct_search(plant_name)

            print(f"✅ Generic search found {len(final_state.get('sources_gathered', []))} sources")
            return final_state

        except Exception as e:
            error_msg = str(e)
            print(f"❌ Generic search failed: {error_msg}")

            # Handle specific Google Generative AI client errors
            if "proxies" in error_msg or "unexpected keyword argument" in error_msg:
                print("⚠️ This appears to be a Google Generative AI client version compatibility issue.")
                print("⚠️ Try updating your google-genai package: pip install --upgrade google-genai")
                print("⚠️ Or check your GEMINI_API_KEY configuration.")

            print("🔄 Falling back to direct search...")
            return self._fallback_direct_search(plant_name)

    def _fallback_direct_search(self, plant_name: str) -> Dict[str, Any]:
        """Fallback direct search when generic search fails."""
        print(f"🔍 Direct search for: {plant_name}")
        
        # Create mock sources for direct search
        mock_sources = [
            {"label": f"{plant_name} Annual Reports", "value": f"{plant_name} annual reports"},
            {"label": f"{plant_name} Investor Relations", "value": f"{plant_name} investor relations"},
            {"label": f"{plant_name} Financial Reports", "value": f"{plant_name} financial reports"}
        ]
        
        return {
            "plant_name": plant_name,
            "sources_gathered": mock_sources,
            "holding_company_name": "",
            "messages": []
        }

    def scrape_with_proper_fallbacks(self, sources: List[Dict[str, Any]], plant_name: str, search_state: Dict[str, Any] = None) -> List[str]:
        """Scrape PDFs using proper fallback order: ScraperAPI → Playwright → Selenium → Direct.

        Args:
            sources: List of source dictionaries
            plant_name: Name of the power plant
            search_state: Optional search state containing AI analysis with URLs

        Returns:
            List of downloaded PDF file paths
        """
        print("🤖 Starting automated PDF scraping with proper fallback order...")
        print("📋 Fallback Order: ScraperAPI → Playwright → Selenium → Direct")

        # Extract URLs from sources (redirect URLs)
        source_urls = self._extract_urls_from_sources(sources, plant_name)

        # Extract AI URLs from search state if available
        ai_urls = []
        if search_state and search_state.get("messages"):
            ai_urls = self._extract_ai_urls_from_state(search_state, plant_name)

        # Combine all URLs for maximum coverage
        all_urls = source_urls.copy()
        for ai_url in ai_urls:
            if ai_url not in all_urls:
                all_urls.append(ai_url)
                print(f"   🎯 Added AI URL: {ai_url}")

        urls = all_urls
        
        if not urls:
            print("❌ No URLs found for scraping")
            return []
        
        print(f"📊 Will attempt to scrape {len(urls)} URL(s)")
        all_downloaded_files = []
        
        # Method 1: ScraperAPI (Primary)
        print("\n🔍 Method 1: Trying ScraperAPI...")
        scraperapi_files = self._try_scraperapi_method(urls, plant_name)
        if scraperapi_files:
            all_downloaded_files.extend(scraperapi_files)
            self.stats["scraperapi_success"] += len(scraperapi_files)
            print(f"✅ ScraperAPI found {len(scraperapi_files)} PDFs")
        
        # Method 2: Playwright (Second fallback)
        if not scraperapi_files:
            print("\n🔄 Method 2: ScraperAPI found no PDFs, trying Playwright...")
            playwright_files = self._try_playwright_method(urls, plant_name)
            if playwright_files:
                all_downloaded_files.extend(playwright_files)
                self.stats["playwright_success"] += len(playwright_files)
                print(f"✅ Playwright found {len(playwright_files)} PDFs")
            
            # Method 3: Selenium (Third fallback)
            if not playwright_files:
                print("\n🔄 Method 3: Playwright found no PDFs, trying Selenium...")
                selenium_files = self._try_selenium_method(urls, plant_name)
                if selenium_files:
                    all_downloaded_files.extend(selenium_files)
                    self.stats["selenium_success"] += len(selenium_files)
                    print(f"✅ Selenium found {len(selenium_files)} PDFs")
                
                # Method 4: Direct search (Final fallback)
                if not selenium_files:
                    print("\n🔄 Method 4: All methods failed, trying direct search...")
                    direct_files = self._try_direct_search_method(plant_name)
                    if direct_files:
                        all_downloaded_files.extend(direct_files)
                        self.stats["direct_success"] += len(direct_files)
                        print(f"✅ Direct search found {len(direct_files)} PDFs")
        
        # Remove duplicates
        unique_files = list(set(all_downloaded_files))
        self.stats["total_pdfs_downloaded"] = len(unique_files)
        
        if unique_files:
            print(f"\n📊 Total unique PDFs downloaded: {len(unique_files)}")
        else:
            print("\n❌ No PDFs were downloaded by any method")
        
        return unique_files

    def _extract_ai_urls_from_state(self, search_state: Dict[str, Any], plant_name: str) -> List[str]:
        """Extract URLs from AI analysis in search state.

        Args:
            search_state: Search state containing AI messages
            plant_name: Name of the power plant

        Returns:
            List of AI-extracted URLs
        """
        ai_urls = []

        if search_state.get("messages"):
            try:
                final_message = search_state["messages"][-1]
                if hasattr(final_message, 'content'):
                    content = final_message.content

                    # Extract URLs from the AI analysis using regex
                    import re
                    found_urls = re.findall(r'https?://[^\s\)\]\*]+', content)

                    # Clean up URLs
                    for url in found_urls:
                        # Remove trailing punctuation and markdown
                        url = url.rstrip('.,;:!?)*')
                        # Remove markdown formatting
                        url = url.replace('**', '')
                        # Remove trailing slash if it's not part of the domain
                        if url.endswith('/') and url.count('/') > 2:
                            url = url.rstrip('/')
                        if url.startswith('http') and not 'grounding-api-redirect' in url:
                            ai_urls.append(url)

            except (IndexError, AttributeError) as e:
                print(f"⚠️ Could not extract AI URLs: {str(e)}")

        if ai_urls:
            print(f"📋 Extracted {len(ai_urls)} AI URLs from analysis")

        return ai_urls


    def _resolve_redirect_url_properly(self, redirect_url: str) -> str:
        """Properly resolve Google grounding API redirect URLs.

        Args:
            redirect_url: The grounding-api-redirect URL

        Returns:
            Resolved actual URL or None if failed
        """
        import requests

        try:
            # Method 1: Try with browser-like headers
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }

            # Try HEAD request first (faster)
            response = requests.head(redirect_url, headers=headers, allow_redirects=True, timeout=15)
            if response.url and response.url != redirect_url:
                return response.url

            # If HEAD fails, try GET request
            response = requests.get(redirect_url, headers=headers, allow_redirects=True, timeout=15)
            if response.url and response.url != redirect_url:
                return response.url

        except Exception as e:
            print(f"      ⚠️ Redirect resolution failed: {str(e)}")

        # Method 2: Try with different session
        try:
            session = requests.Session()
            session.headers.update(headers)
            response = session.get(redirect_url, allow_redirects=True, timeout=15)
            if response.url and response.url != redirect_url:
                return response.url
        except Exception as e:
            print(f"      ⚠️ Session redirect resolution failed: {str(e)}")

        return None

    def _extract_urls_from_sources(self, sources: List[Dict[str, Any]], plant_name: str) -> List[str]:
        """Extract URLs from sources with improved redirect resolution.

        Properly resolves redirect URLs using browser-like headers and methods.
        """
        all_urls = []

        # Extract from sources with proper redirect resolution
        for source in sources:
            if source.get('value') and source['value'].startswith('http'):
                original_url = source['value']

                # For redirect URLs, use improved resolution method
                if "grounding-api-redirect" in original_url:
                    print(f"   🔍 Resolving redirect: {original_url[:60]}...")
                    resolved_url = self._resolve_redirect_url_properly(original_url)
                    if resolved_url and resolved_url != original_url:
                        print(f"      ✅ Resolved to: {resolved_url}")
                        # Clean up resolved URL
                        if resolved_url.endswith('/') and resolved_url.count('/') > 2:
                            resolved_url = resolved_url.rstrip('/')
                        all_urls.append(resolved_url)
                    else:
                        print(f"      ❌ Could not resolve redirect URL")
                else:
                    # Clean up direct URL - remove trailing slash if it's not part of the domain
                    if original_url.endswith('/') and original_url.count('/') > 2:
                        original_url = original_url.rstrip('/')
                    all_urls.append(original_url)

        # Remove duplicates while preserving order
        unique_urls = []
        seen = set()
        for url in all_urls:
            if url not in seen:
                unique_urls.append(url)
                seen.add(url)

        if unique_urls:
            print(f"📋 Extracted {len(unique_urls)} unique working URLs from sources")
            for url in unique_urls:
                print(f"   🎯 Source URL: {url}")
            print(f"💡 Strategy: Collect PDFs from ALL sources, then use OpenAI classification to filter")
        else:
            print("⚠️ No working URLs could be extracted from sources")

        return unique_urls

    def _try_scraperapi_method(self, urls: List[str], plant_name: str) -> List[str]:
        """Try ScraperAPI method."""
        try:
            return self.scraper_api.scrape_annual_reports(urls, plant_name)
        except Exception as e:
            print(f"❌ ScraperAPI method failed: {str(e)}")
            return []

    def _try_playwright_method(self, urls: List[str], plant_name: str) -> List[str]:
        """Try Playwright method."""
        try:
            return download_pdfs_advanced(urls, plant_name, self.download_dir)
        except Exception as e:
            print(f"❌ Playwright method failed: {str(e)}")
            return []

    def _try_selenium_method(self, urls: List[str], plant_name: str) -> List[str]:
        """Try Selenium method."""
        try:
            if not self.selenium_scraper:
                self.selenium_scraper = PDFScraper(download_dir=self.download_dir, headless=True)
            
            files = self.selenium_scraper.scrape_annual_reports(urls, plant_name)
            self.selenium_scraper.cleanup()
            return files
        except Exception as e:
            print(f"❌ Selenium method failed: {str(e)}")
            if self.selenium_scraper:
                self.selenium_scraper.cleanup()
            return []

    def _try_direct_search_method(self, plant_name: str) -> List[str]:
        """Try direct search method."""
        try:
            return self.scraper_api.run_downloader(plant_name, 2020, 2024)
        except Exception as e:
            print(f"❌ Direct search method failed: {str(e)}")
            return []

    def extract_pdf_text_fast(self, pdf_path: str, max_pages: int = 3) -> str:
        """Fast PDF text extraction using multiple methods with fallbacks.

        Args:
            pdf_path: Path to PDF file
            max_pages: Maximum pages to extract (reduced for speed)

        Returns:
            Extracted text content
        """
        # Method 1: PyMuPDF (fastest)
        if PYMUPDF_AVAILABLE:
            try:
                import fitz
                doc = fitz.open(pdf_path)
                text = ""
                for page_num in range(min(max_pages, len(doc))):
                    page = doc.load_page(page_num)
                    text += page.get_text() + "\n"
                doc.close()
                if text.strip():
                    return text.strip()
            except Exception as e:
                print(f"⚠️ PyMuPDF extraction failed: {e}")

        # Method 2: pdfplumber (good balance of speed and accuracy)
        if PDFPLUMBER_AVAILABLE:
            try:
                import pdfplumber
                text = ""
                with pdfplumber.open(pdf_path) as pdf:
                    for page_num in range(min(max_pages, len(pdf.pages))):
                        page = pdf.pages[page_num]
                        page_text = page.extract_text()
                        if page_text:
                            text += page_text + "\n"
                if text.strip():
                    return text.strip()
            except Exception as e:
                print(f"⚠️ pdfplumber extraction failed: {e}")

        # Method 3: PyPDF2 (fallback)
        if PYPDF2_AVAILABLE:
            try:
                import PyPDF2
                text = ""
                with open(pdf_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    for page_num in range(min(max_pages, len(pdf_reader.pages))):
                        page = pdf_reader.pages[page_num]
                        text += page.extract_text() + "\n"
                if text.strip():
                    return text.strip()
            except Exception as e:
                print(f"⚠️ PyPDF2 extraction failed: {e}")

        # Method 4: Original UnstructuredPDFLoader (slowest, last resort)
        try:
            loader = UnstructuredPDFLoader(
                file_path=pdf_path,
                strategy="fast",  # Use fast strategy
                languages=["eng"]
            )
            documents = loader.load()

            # Combine content of only first N pages
            text = ""
            for i, doc in enumerate(documents):
                if i >= max_pages:
                    break
                text += doc.page_content + "\n"

            return text.strip()

        except Exception as e:
            print(f"❌ All PDF text extraction methods failed for {os.path.basename(pdf_path)}: {e}")
            return ""

    def extract_pdf_text(self, pdf_path: str, max_pages: int = 3) -> str:
        """Legacy method - redirects to fast extraction."""
        return self.extract_pdf_text_fast(pdf_path, max_pages)

    def classify_pdf_as_consolidated(self, text: str, plant_name: str, model: str = "gpt-4o-mini") -> bool:
        """Classify PDF using improved logic: keep standalone, delete consolidated-only, keep both.

        Args:
            text: Extracted text from PDF
            plant_name: Name of the power plant
            model: OpenAI model to use (default: gpt-4o-mini for speed)

        Returns:
            True if PDF should be kept, False if it should be deleted
        """
        # Check if OpenAI client is available
        if self.openai_client is None:
            print("⚠️ OpenAI client not available, using enhanced fallback classification")
            return self._enhanced_fallback_classification(text, plant_name)

        # Optimize text size - take first 20,000 characters instead of 100,000
        # This significantly reduces API costs and processing time
        optimized_text = text[:20000]
        if len(text) > 20000:
            optimized_text += "\n\n[Text truncated for efficiency]"

        system_prompt = (
            "You are a fast document classifier. Analyze the document excerpt and answer YES/NO for each question. "
            "Be concise and decisive."
        )

        user_prompt = f"""
        Document excerpt analysis for power plant: "{plant_name}"

        Questions (answer YES/NO only):
        1. Annual report: Is this an annual report?
        2. Belongs to plant: Does this belong to "{plant_name}"?
        3. Has standalone: Contains standalone financial reports?
        4. Has consolidated: Contains consolidated financial reports?
        5. Has both in same PDF: Contains both standalone AND consolidated?

        Format:
        Annual report: YES/NO
        Belongs to plant: YES/NO
        Has standalone: YES/NO
        Has consolidated: YES/NO
        Has both in same PDF: YES/NO

        Text excerpt:
        {optimized_text}
        """

        try:
            response = self.openai_client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0,
                max_tokens=200  # Limit response size for speed
            )

            content = response.choices[0].message.content
            print("🔍 OpenAI Classification:\n" + content.strip() + "\n")

            # Convert to lowercase for consistent checks
            content_lower = content.lower()

            # Extract flags
            is_annual = "annual report: yes" in content_lower
            is_correct_plant = "belongs to plant: yes" in content_lower
            has_standalone = "has standalone: yes" in content_lower
            has_consolidated = "has consolidated: yes" in content_lower
            has_both_in_same = "has both in same pdf: yes" in content_lower

            # Decision logic based on requirements:
            # 1. Keep standalone reports (delete consolidated)
            # 2. If consolidated available → delete it
            # 3. If both standalone + consolidated in single PDF → keep it

            if not (is_annual and is_correct_plant):
                print("❌ Not a valid annual report for this plant")
                return False

            if has_both_in_same:
                print("✅ KEEP: Contains both standalone and consolidated in same PDF")
                return True
            elif has_standalone and not has_consolidated:
                print("✅ KEEP: Contains standalone reports only")
                return True
            elif has_consolidated and not has_standalone:
                print("🗑️ DELETE: Contains only consolidated reports")
                return False
            else:
                print("⚠️ UNCERTAIN: Could not determine report type clearly, keeping as fallback")
                return True  # Keep uncertain cases to be safe

        except Exception as e:
            print(f"❌ Error classifying PDF: {e}")
            # Fallback to enhanced classification
            return self._enhanced_fallback_classification(text, plant_name)

    def _process_single_pdf(self, pdf_path: str, plant_name: str) -> tuple[str, bool, str]:
        """Process a single PDF for classification.

        Args:
            pdf_path: Path to PDF file
            plant_name: Name of the power plant

        Returns:
            Tuple of (pdf_path, should_keep, extracted_text)
        """
        print(f"🔍 Processing: {os.path.basename(pdf_path)}")

        # Extract text from PDF (fast method)
        text = self.extract_pdf_text_fast(pdf_path, max_pages=3)  # Reduced pages for speed
        if not text:
            print(f"❌ Could not extract text: {os.path.basename(pdf_path)}")
            return pdf_path, False, ""

        # Classify PDF using improved logic
        should_keep = self.classify_pdf_as_consolidated(text, plant_name)
        return pdf_path, should_keep, text

    def auto_classify_and_delete_non_consolidated_parallel(self, pdf_files: List[str], plant_name: str, max_workers: int = 3) -> List[str]:
        """Automatically classify PDFs in parallel for faster processing.

        Args:
            pdf_files: List of PDF file paths
            plant_name: Name of the power plant
            max_workers: Maximum number of parallel workers

        Returns:
            List of valid annual report paths (standalone or both standalone+consolidated)
        """
        print(f"\n📋 Automatically classifying {len(pdf_files)} PDFs in parallel...")
        print("🤖 Classification Rules:")
        print("   ✅ KEEP: Standalone reports")
        print("   ✅ KEEP: PDFs with both standalone + consolidated")
        print("   🗑️ DELETE: Consolidated-only reports")
        print(f"⚡ Using {max_workers} parallel workers for faster processing")

        valid_pdfs = []

        # Process PDFs in parallel
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all PDF processing tasks
            future_to_pdf = {
                executor.submit(self._process_single_pdf, pdf_path, plant_name): pdf_path
                for pdf_path in pdf_files
            }

            # Collect results as they complete
            for future in concurrent.futures.as_completed(future_to_pdf):
                pdf_path, should_keep, text = future.result()

                if should_keep and text:  # Only keep if we have text and classification says keep
                    print(f"✅ KEEPING: {os.path.basename(pdf_path)}")
                    valid_pdfs.append(pdf_path)
                    self.stats["valid_consolidated_reports"] += 1
                else:
                    print(f"🗑️ DELETING: {os.path.basename(pdf_path)}")
                    self._delete_pdf(pdf_path)
                    self.stats["deleted_non_consolidated"] += 1

        print(f"\n📊 Classification Results:")
        print(f"   ✅ Kept: {len(valid_pdfs)} consolidated annual reports")
        print(f"   🗑️ Deleted: {self.stats['deleted_non_consolidated']} non-consolidated PDFs")

        return valid_pdfs

    def auto_classify_and_delete_non_consolidated(self, pdf_files: List[str], plant_name: str) -> List[str]:
        """Automatically classify PDFs using improved logic: keep standalone, delete consolidated-only.

        This method now uses parallel processing for faster execution.

        Args:
            pdf_files: List of PDF file paths
            plant_name: Name of the power plant

        Returns:
            List of valid annual report paths (standalone or both standalone+consolidated)
        """
        # Use parallel processing if we have multiple PDFs
        if len(pdf_files) > 1:
            return self.auto_classify_and_delete_non_consolidated_parallel(pdf_files, plant_name)

        # Single PDF - process normally
        print(f"\n📋 Automatically classifying {len(pdf_files)} PDF...")
        print("🤖 Classification Rules:")
        print("   ✅ KEEP: Standalone reports")
        print("   ✅ KEEP: PDFs with both standalone + consolidated")
        print("   🗑️ DELETE: Consolidated-only reports")

        valid_pdfs = []

        for pdf_path in pdf_files:
            pdf_path, should_keep, text = self._process_single_pdf(pdf_path, plant_name)

            if should_keep and text:
                print(f"✅ KEEPING: {os.path.basename(pdf_path)}")
                valid_pdfs.append(pdf_path)
                self.stats["valid_consolidated_reports"] += 1
            else:
                print(f"🗑️ DELETING: {os.path.basename(pdf_path)}")
                self._delete_pdf(pdf_path)
                self.stats["deleted_non_consolidated"] += 1

        print(f"\n📊 Classification Results:")
        print(f"   ✅ Kept: {len(valid_pdfs)} consolidated annual reports")
        print(f"   🗑️ Deleted: {self.stats['deleted_non_consolidated']} non-consolidated PDFs")

        return valid_pdfs

    def _delete_pdf(self, pdf_path: str) -> bool:
        """Delete a PDF file.

        Args:
            pdf_path: Path to PDF file to delete

        Returns:
            True if deleted successfully, False otherwise
        """
        try:
            os.remove(pdf_path)
            return True
        except Exception as e:
            print(f"❌ Failed to delete {os.path.basename(pdf_path)}: {e}")
            return False

    def run_comprehensive_automation(self, plant_name: str) -> Dict[str, Any]:
        """Run the complete automated process.

        Args:
            plant_name: Name of the power plant

        Returns:
            Dictionary with results and statistics
        """
        print("🚀 STARTING COMPREHENSIVE AUTOMATED SCRAPER")
        print("=" * 60)
        print(f"🏭 Target: {plant_name}")
        print("🤖 Mode: Fully Automated (No user prompts)")
        print("📋 Process: Search → Scrape → Classify → Delete Invalid")
        print("=" * 60)

        start_time = time.time()

        # Step 1: Generic search for annual reports
        print("\n📍 STEP 1: Generic AI-powered search")
        search_results = self.generic_search_for_annual_reports(plant_name)

        if not search_results.get("sources_gathered"):
            print("❌ No sources found, cannot proceed")
            return {"success": False, "error": "No sources found"}

        # Step 2: Scrape PDFs with proper fallback order
        print("\n📍 STEP 2: Multi-method PDF scraping")
        downloaded_pdfs = self.scrape_with_proper_fallbacks(
            search_results["sources_gathered"],
            plant_name,
            search_results  # Pass search state for AI URL extraction
        )

        if not downloaded_pdfs:
            print("❌ No PDFs downloaded, process complete")
            return {"success": False, "error": "No PDFs downloaded"}

        # Step 3: Automatic classification and deletion
        print("\n📍 STEP 3: Automatic PDF classification")
        valid_pdfs = self.auto_classify_and_delete_non_consolidated(downloaded_pdfs, plant_name)

        # Calculate final statistics
        end_time = time.time()
        duration = end_time - start_time

        results = {
            "success": True,
            "plant_name": plant_name,
            "duration_seconds": round(duration, 2),
            "total_pdfs_downloaded": len(downloaded_pdfs),
            "valid_consolidated_reports": len(valid_pdfs),
            "deleted_non_consolidated": len(downloaded_pdfs) - len(valid_pdfs),
            "final_pdf_paths": valid_pdfs,
            "method_stats": {
                "scraperapi_success": self.stats["scraperapi_success"],
                "playwright_success": self.stats["playwright_success"],
                "selenium_success": self.stats["selenium_success"],
                "direct_success": self.stats["direct_success"]
            }
        }

        # Print final summary
        print("\n" + "=" * 60)
        print("🎯 COMPREHENSIVE AUTOMATION COMPLETE")
        print("=" * 60)
        print(f"⏱️ Duration: {duration:.2f} seconds")
        print(f"📥 Total PDFs Downloaded: {len(downloaded_pdfs)}")
        print(f"✅ Valid Consolidated Reports: {len(valid_pdfs)}")
        print(f"🗑️ Deleted Non-Consolidated: {len(downloaded_pdfs) - len(valid_pdfs)}")

        if valid_pdfs:
            print(f"\n📁 Final PDFs saved in: ./downloads/{plant_name.replace(' ', '_')}/")
            for pdf_path in valid_pdfs:
                print(f"   📄 {os.path.basename(pdf_path)}")

        print("=" * 60)

        return results


def main():
    """Main function for command-line usage."""
    print("🤖 COMPREHENSIVE AUTOMATED POWER PLANT SCRAPER")
    print("=" * 60)
    print("Features:")
    print("• Generic AI-powered search (no hardcoding)")
    print("• Proper fallback: ScraperAPI → Playwright → Selenium → Direct")
    print("• Automatic PDF scraping (no user prompts)")
    print("• OpenAI classification & auto-deletion of non-consolidated reports")
    print("=" * 60)

    # Check required environment variables
    required_vars = ["GEMINI_API_KEY", "OPENAI_API_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]

    if missing_vars:
        print("❌ Missing required environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\nPlease set these variables before running the scraper.")
        return

    # Optional but recommended
    if not os.getenv("SCRAPER_API_KEY"):
        print("⚠️ Warning: SCRAPER_API_KEY not set (ScraperAPI fallback will be limited)")

    while True:
        try:
            # Get power plant name
            plant_name = input("\nEnter power plant name (or 'quit' to exit): ").strip()

            if plant_name.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break

            if not plant_name:
                print("❌ Please enter a valid power plant name.")
                continue

            # Initialize scraper
            scraper = ComprehensiveAutomatedScraper(download_dir="./downloads")

            # Run comprehensive automation
            results = scraper.run_comprehensive_automation(plant_name)

            if results["success"]:
                print(f"\n🎉 SUCCESS! Found {results['valid_consolidated_reports']} consolidated annual reports")
            else:
                print(f"\n❌ Process failed: {results.get('error', 'Unknown error')}")

            # Ask if user wants to process another plant
            print("\n" + "-" * 50)
            continue_search = input("Process another power plant? (y/n): ").strip().lower()
            if continue_search not in ['y', 'yes']:
                print("👋 Goodbye!")
                break

        except KeyboardInterrupt:
            print("\n\n👋 Process interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Unexpected error: {str(e)}")
            continue


if __name__ == "__main__":
    main()
