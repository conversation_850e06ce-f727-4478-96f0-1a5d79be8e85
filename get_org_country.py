import google.generativeai as genai
import json
import re

# Configure the Gemini API
genai.configure(api_key="AIzaSyAw9JtsEZ3_7rrUpMjbN7rm6l5zimuE4Fo")

# Create the model
model = genai.GenerativeModel("gemini-2.5-flash")

def extract_json_from_text(text):
    """
    Try to extract a JSON block from text using regex.
    """
    try:
        match = re.search(r'\{.*\}', text, re.DOTALL)
        if match:
            return json.loads(match.group())
    except Exception as e:
        print("JSON extraction error:", e)
    return None

def get_power_plant_details(power_plant_name: str) -> dict:
    prompt = f"""
    Given the power plant named "{power_plant_name}", extract the following information:
    - The name of the organization or company that owns or operates it (org_name)
    - The country where the power plant is located (country)

    Respond only in the following JSON format (no extra text or explanation):

    {{
        "power_plant_name": "{power_plant_name}",
        "org_name": "<organization name>",
        "country": "<country>"
    }}
    """

    try:
        response = model.generate_content(prompt)
        content = response.text.strip()

        # Try direct JSON first
        try:
            return json.loads(content)
        except json.JSONDecodeError:
            result = extract_json_from_text(content)
            if result:
                return result

    except Exception as e:
        print("Error:", e)

    return {
        "power_plant_name": power_plant_name,
        "org_name": "Not found",
        "country": "Not found"
    }

