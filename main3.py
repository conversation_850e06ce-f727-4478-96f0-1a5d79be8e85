import requests
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import Dict

from get_org_country import get_power_plant_details
from get_org_id import generate_org_uid
# from get_annualreports import has_annual_report
from power_plant_search_function import search_power_plant_reports,has_annual_report,move_pdfs_to_pipeline_downloads

app = FastAPI()

class PipelineRequest(BaseModel):
    plantName: str
    Product: str

@app.post("/run-pipeline")
def run_pipeline(input_data: PipelineRequest) -> Dict:
    plant_name = input_data.plantName
    domain_name = input_data.Product

    if not plant_name or not domain_name:
        raise HTTPException(status_code=400, detail="Both 'plantName' and 'Product' are required.")

    try:
        print("📄 Running pipeline for plant:", plant_name)

        # Optional: Uncomment if you want to download first
        search_power_plant_reports(plant_name)

        # Check if annual report exists
        annual_report = has_annual_report()
        print("✅ annual_report =", annual_report)

        if annual_report:
            move_pdfs_to_pipeline_downloads("./downloads","/Users/<USER>/Documents/mlprojects/Transition-agi-fin_new_pipeline/downloads")
            org_details = get_power_plant_details(plant_name)
            org_name = org_details.get("org_name")
            country = org_details.get("country")
            uid_info = generate_org_uid(org_name, country, plant_name)

            print("🌍 org_name =", org_name)
            print("🌎 country =", country)
            print("🆔 uid_info =", uid_info)

            # Check if correct product
            if domain_name.strip().lower() == "clem-transition":
                print("🚀 Entered clem-transition logic block")

                # 🔁 First Pipeline API (Local)
                first_api_url = "http://127.0.0.1:8000/api/process/clem_trans_financial"
                first_api_payload = {
                    "plant_name": plant_name,
                    "country_name": country,
                    "entity_id": uid_info.get("org_uid")
                }
                try:
                    first_response = requests.post(first_api_url, data=first_api_payload, timeout=10)
                    first_response.raise_for_status()
                    print("✅ First API response:", first_response.status_code, first_response.text)
                except requests.exceptions.RequestException as e:
                    print("❌ First API request failed:", str(e))

                # 🔁 Second Pipeline API (Dev Tunnel)
                second_api_url = "https://pw7kk1s0-8000.inc1.devtunnels.ms/api/v1/extraction/agi-plant"
                second_api_payload = {
                    "plant_name": plant_name,
                    "entity_id": uid_info.get("org_uid")
                }
                print("📡 Calling Second API with payload:", second_api_payload)
                try:
                    second_response = requests.post(
                        second_api_url,
                        json=second_api_payload,
                        timeout=10
                    )
                    second_response.raise_for_status()
                    print("✅ Second API status code:", second_response.status_code)
                    print("✅ Second API response:", second_response.text)
                except requests.exceptions.RequestException as e:
                    print("❌ Second API request failed:", str(e))

            return {
                "status": "success",
                "message": f"Pipeline triggered for {plant_name}",
                "org_name": org_name,
                "country": country,
                "uid": uid_info.get("org_uid")
            }
        else:
            print("⚠️ Skipping pipeline: No annual report found.")
            return {
                "status": "skipped",
                "message": f"No annual report found for {plant_name}"
            }

    except Exception as e:
        print("🔥 Unhandled Exception:", str(e))
        raise HTTPException(status_code=500, detail=str(e))
