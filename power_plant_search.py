#!/usr/bin/env python3
"""
Power Plant Annual Report Search Terminal Interface

This script provides a simple terminal interface to test the power plant
annual report search functionality without requiring the frontend.
"""

import os
import sys
import requests
from typing import Dict, Any, List
from langchain_core.messages import HumanMessage

# Add the src directory to the path so we can import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from agent.graph import power_plant_graph
from agent.configuration import Configuration
from agent.pdf_scraper import PDFScraper
from agent.scraper_api_pdf_scraper import ScraperAP<PERSON>DFScraper
from agent.content_analyzer import ContentAnalyzer
from agent.comprehensive_automated_scraper import ComprehensiveAutomatedScraper


def initialize_power_plant_state(plant_name: str) -> Dict[str, Any]:
    """Initialize the state for power plant annual report search.
    
    Args:
        plant_name: Name of the power plant to search for
        
    Returns:
        Dictionary containing the initial state for the search
    """
    return {
        "messages": [HumanMessage(content=plant_name)],
        "plant_name": plant_name,
        "search_phase": "direct_search",
        "search_query": [],
        "web_research_result": [],
        "sources_gathered": [],
        "annual_report_urls": [],
        "initial_search_query_count": 2,
        "max_research_loops": 2,
        "research_loop_count": 0,
        "reasoning_model": "gemini-2.5-flash",
        "holding_company_name": "",
        "found_annual_reports": False,
    }


def resolve_redirect_url_properly(redirect_url: str) -> str:
    """Properly resolve Google grounding API redirect URLs.

    Args:
        redirect_url: The grounding-api-redirect URL

    Returns:
        Resolved actual URL or None if failed
    """
    import requests

    try:
        # Method 1: Try with browser-like headers
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }

        # Try HEAD request first (faster)
        response = requests.head(redirect_url, headers=headers, allow_redirects=True, timeout=15)
        if response.url and response.url != redirect_url:
            return response.url

        # If HEAD fails, try GET request
        response = requests.get(redirect_url, headers=headers, allow_redirects=True, timeout=15)
        if response.url and response.url != redirect_url:
            return response.url

    except Exception as e:
        print(f"      ⚠️ Redirect resolution failed: {str(e)}")

    # Method 2: Try with different session
    try:
        session = requests.Session()
        session.headers.update(headers)
        response = session.get(redirect_url, allow_redirects=True, timeout=15)
        if response.url and response.url != redirect_url:
            return response.url
    except Exception as e:
        print(f"      ⚠️ Session redirect resolution failed: {str(e)}")

    return None


def correct_known_url_patterns(url: str, plant_name: str) -> str:
    """Correct known URL patterns that are commonly wrong in AI analysis.

    Args:
        url: Original URL from AI analysis
        plant_name: Name of the power plant

    Returns:
        Corrected URL
    """
    # Only apply corrections as fallback, not as primary method
    # This should be used sparingly and only for known problematic patterns

    return url


def print_separator():
    """Print a visual separator for better output formatting."""
    print("\n" + "="*80 + "\n")


def print_search_results(final_state: Dict[str, Any]):
    """Print the final search results in a formatted way.

    Args:
        final_state: The final state from the graph execution
    """
    # Check if final_state is None or empty
    if not final_state:
        print_separator()
        print("❌ No search results available")
        print_separator()
        return
        
    print_separator()
    print("🔍 POWER PLANT ANNUAL REPORT SEARCH RESULTS")
    print_separator()

    plant_name = final_state.get("plant_name", "Unknown")
    print(f"Power Plant: {plant_name}")

    holding_company = final_state.get("holding_company_name", "")
    if holding_company:
        print(f"Holding Company: {holding_company}")

    search_phase = final_state.get("search_phase", "direct_search")
    print(f"Search Phase: {search_phase.replace('_', ' ').title()}")

    print_separator()

    # Check if the results are likely incorrect (mismatch between search and results)
    result_mismatch = False
    result_content = ""
    
    # Print the final answer
    if final_state.get("messages"):
        try:
            final_message = final_state["messages"][-1]
            if hasattr(final_message, 'content'):
                result_content = final_message.content
                print("📄 ANNUAL REPORT FINDINGS:")
                print(result_content)
                
                # Improved mismatch detection - handle different formatting
                plant_name_lower = plant_name.lower()
                content_lower = result_content.lower()

                # Normalize plant name for comparison (remove underscores, extra spaces)
                normalized_plant_name = plant_name_lower.replace('_', ' ').replace('-', ' ')
                normalized_plant_name = ' '.join(normalized_plant_name.split())  # Remove extra spaces

                # Check if normalized plant name is in content
                if normalized_plant_name not in content_lower:
                    # Extract key words from plant name (ignore common words)
                    plant_words = normalized_plant_name.split()
                    key_words = [word for word in plant_words if len(word) > 3 and word not in ['limited', 'company', 'power', 'plant', 'energy']]

                    # Check if at least 2 key words are found (or 1 if only 1 key word exists)
                    found_words = [word for word in key_words if word in content_lower]
                    required_matches = min(2, len(key_words)) if len(key_words) > 1 else 1

                    if len(found_words) < required_matches:
                        result_mismatch = True
                        print("\n⚠️  WARNING: Search results may not match requested power plant!")
                        print(f"   Requested: {plant_name}")
                        print(f"   Key words found: {found_words} (need {required_matches})")
                        print("   Results may be for a different company")
                
        except (IndexError, AttributeError) as e:
            print("❌ Error displaying results: No valid content found")
            print(f"Error details: {str(e)}")

    print_separator()

    # Print sources found
    sources = final_state.get("sources_gathered", [])
    if sources:
        print("🔗 SOURCES FOUND:")
        for i, source in enumerate(sources, 1):
            print(f"{i}. {source.get('label', 'Unknown Source')}")
            print(f"   URL: {source.get('value', 'No URL')}")
            print()
    else:
        print("❌ No sources found")
    
    # If we detected a mismatch, suggest using direct search instead
    if result_mismatch:
        print("\n⚠️  The search results don't appear to match your request.")
        print("   This can happen when the search engine returns incorrect results.")
        print("   Recommend using direct PDF search instead.")
        
    print_separator()
    
    # Return whether there was a mismatch (for use by the calling function)
    return result_mismatch


def scrape_annual_report_pdfs(sources: List[Dict[str, Any]], plant_name: str, final_state: Dict[str, Any] = None):
    """Scrape PDF annual reports from the identified sources.

    Args:
        sources: List of source dictionaries with URLs
        plant_name: Name of the power plant
        final_state: Final state containing the AI's answer with URLs
    """
    print("🤖 Starting PDF scraping process...")
    print("⚠️  Using ScraperAPI for more reliable PDF downloading...")

    try:
        # Check if SCRAPER_API_KEY is set
        if not os.getenv("SCRAPER_API_KEY"):
            print("❌ Error: SCRAPER_API_KEY environment variable is not set.")
            print("Please set your ScraperAPI key before running this script.")
            print("Example: export SCRAPER_API_KEY='your-api-key-here'")
            return

        # Extract URLs from sources
        source_urls = [source.get('value', '') for source in sources if source.get('value')]
        source_urls = [url for url in source_urls if url.startswith('http')]

        # PROPERLY RESOLVE redirect URLs with better headers and methods
        print("🔍 Resolving redirect URLs with improved method...")
        resolved_urls = []

        for i, url in enumerate(source_urls, 1):
            try:
                print(f"   Processing URL {i}/{len(source_urls)}: {url[:60]}...")

                # For redirect URLs, use better resolution method
                if "grounding-api-redirect" in url:
                    resolved_url = resolve_redirect_url_properly(url)
                    if resolved_url and resolved_url != url:
                        print(f"      ✅ Resolved to: {resolved_url}")
                        resolved_urls.append(resolved_url)
                    else:
                        print(f"      ❌ Could not resolve redirect URL")
                else:
                    print(f"      Direct URL: {url}")
                    resolved_urls.append(url)

            except Exception as e:
                print(f"   ❌ Error processing URL: {url[:50]}... ({str(e)})")
                continue

        # Extract AI URLs as backup only (they may be parent company URLs)
        answer_urls = []
        if final_state and final_state.get("messages"):
            final_message = final_state["messages"][-1]
            if hasattr(final_message, 'content'):
                answer = final_message.content

                # Extract URLs from the answer using regex
                import re
                found_urls = re.findall(r'https?://[^\s\)\]\*]+', answer)

                # Clean up URLs (remove trailing punctuation and markdown)
                clean_urls = []
                for url in found_urls:
                    # Remove trailing punctuation and markdown
                    url = url.rstrip('.,;:!?)*')
                    # Remove markdown formatting
                    url = url.replace('**', '')
                    # Remove trailing slash if it's not part of the domain
                    if url.endswith('/') and url.count('/') > 2:
                        url = url.rstrip('/')
                    if url.startswith('http') and not 'grounding-api-redirect' in url:
                        clean_urls.append(url)

                answer_urls = clean_urls

        # COMBINE ALL URLS - both resolved redirect URLs and AI URLs for maximum PDF collection
        all_urls = []

        if resolved_urls:
            print(f"📊 Found {len(resolved_urls)} resolved URLs from sources:")
            for url in resolved_urls:
                print(f"   🎯 Resolved URL: {url}")
                all_urls.append(url)

        if answer_urls:
            print(f"📊 Found {len(answer_urls)} AI-extracted URLs from analysis:")
            for url in answer_urls:
                print(f"   🎯 AI URL: {url}")
                # Add AI URLs if they're not already in the list
                if url not in all_urls:
                    all_urls.append(url)
                else:
                    print(f"   ⚠️  Duplicate URL skipped: {url}")

        # Show final combined list
        if all_urls:
            print(f"\n📋 COMBINED URL LIST - Will scrape ALL sources for maximum PDF collection:")
            for i, url in enumerate(all_urls, 1):
                print(f"   {i}. {url}")
            print(f"\n💡 Strategy: Collect PDFs from ALL sources, then use OpenAI classification to filter")
        else:
            print("❌ No URLs found from any source")

        # Show domain analysis for information
        if resolved_urls and answer_urls:
            resolved_domains = [url.split('/')[2] for url in resolved_urls]
            ai_domains = [url.split('/')[2] for url in answer_urls]
            different_domains = [domain for domain in ai_domains if domain not in resolved_domains]
            if different_domains:
                print(f"   📊 Info: Found URLs from multiple domains: {set(resolved_domains + ai_domains)}")
                print(f"   📊 This increases chances of finding all available annual reports")

        all_urls = [url for url in all_urls if url.startswith('http')]

        if not all_urls:
            print("❌ No valid URLs found for scraping.")
            return

        # URLs are already resolved above, so we can use them directly
        resolved_urls = all_urls

        # Step 2: Use content analyzer to find the best annual report URLs
        print(f"\n🧠 Analyzing content of {len(resolved_urls)} URLs...")
        content_analyzer = ContentAnalyzer()
        best_urls = content_analyzer.get_best_annual_report_urls(resolved_urls, max_results=3)

        if not best_urls:
            print("⚠️ No relevant annual report content found, using original URLs as fallback")
            # Deduplicate by domain as fallback
            unique_urls = []
            seen_domains = set()
            for url in resolved_urls:
                domain = url.split('/')[2] if '/' in url else url
                if domain not in seen_domains:
                    unique_urls.append(url)
                    seen_domains.add(domain)
                    if len(unique_urls) >= 3:
                        break
        else:
            unique_urls = best_urls
            print(f"✅ Selected {len(unique_urls)} best URLs based on content analysis")

        if not unique_urls:
            print("❌ No unique URLs found after deduplication.")
            return

        print(f"\n📊 Will scrape {len(unique_urls)} unique URL(s)")

        # Use comprehensive scraper with proper fallback order: ScraperAPI → Playwright → Selenium → Direct
        print("🔍 Using comprehensive scraper with proper fallback order...")
        print("📋 Fallback Order: ScraperAPI → Playwright → Selenium → Direct")

        comprehensive_scraper = ComprehensiveAutomatedScraper(download_dir="./downloads")

        # Create mock sources from URLs for the comprehensive scraper
        mock_sources = [{"label": f"Source {i+1}", "value": url} for i, url in enumerate(unique_urls)]

        # Use the comprehensive scraper's fallback system with search state for AI URLs
        downloaded_files = comprehensive_scraper.scrape_with_proper_fallbacks(mock_sources, plant_name, final_state)
        
        # If no PDFs found, try direct search using comprehensive scraper
        if not downloaded_files:
            print("\n🔄 No PDFs found on resolved URLs, trying direct search...")

            additional_files = comprehensive_scraper._try_direct_search_method(plant_name)
            if additional_files:
                downloaded_files.extend(additional_files)

        # If no PDFs found, try generic alternative search patterns
        if not downloaded_files:
            print("\n🔄 No PDFs found, trying generic alternative search...")

            # Use comprehensive scraper for generic alternative search
            additional_files = comprehensive_scraper._try_direct_search_method(plant_name)
            if additional_files:
                downloaded_files.extend(additional_files)
                print(f"   ✅ Found {len(additional_files)} PDFs through generic search")
                            
            # If still no PDFs found, try direct search for the holding company
            if not downloaded_files and final_state and final_state.get('holding_company_name'):
                holding_company = final_state.get('holding_company_name')
                print(f"\n🔄 Trying direct search for holding company: {holding_company}")

                # Use comprehensive scraper for holding company search
                additional_files = comprehensive_scraper._try_direct_search_method(holding_company)
                if additional_files:
                    downloaded_files.extend(additional_files)

        if downloaded_files:
            # Remove duplicates from downloaded files
            unique_files = list(set(downloaded_files))
            print(f"\n✅ Successfully downloaded {len(unique_files)} unique PDF(s):")
            for file_path in unique_files:
                print(f"   📄 {file_path}")
            print(f"\n📁 Files saved in: ./downloads/{plant_name.replace(' ', '_')}/")

            # Add classification step
            print("\n" + "="*60)
            print("🤖 STARTING PDF CLASSIFICATION")
            print("="*60)

            try:
                # Try the simple OpenAI classifier first (no problematic imports)
                import sys
                sys.path.insert(0, os.path.dirname(__file__))
                from simple_openai_classifier import process_pdf_list_simple
                print(f"🔍 Classifying {len(unique_files)} PDFs using fast OpenAI classifier...")
                final_files = process_pdf_list_simple(unique_files, plant_name)
                print(f"\n📊 Final result: {len(final_files)} PDFs kept after OpenAI classification")
            except ImportError:
                print("⚠️ Simple OpenAI classifier not available, trying improved classifier...")
                try:
                    from improved_pdf_classifier import process_pdf_list_with_validation
                    print(f"🔍 Classifying {len(unique_files)} PDFs using improved logic...")
                    process_pdf_list_with_validation(unique_files, plant_name)
                except Exception as e:
                    print(f"❌ Error during classification: {e}")
                    print("📄 All downloaded files have been kept without classification")
            except Exception as e:
                print(f"❌ Error during OpenAI classification: {e}")
                print("🔄 Falling back to basic classification...")
                try:
                    # Fallback to comprehensive scraper's classification
                    final_files = comprehensive_scraper.auto_classify_and_delete_non_consolidated(unique_files, plant_name)
                    print(f"\n📊 Final result: {len(final_files)} PDFs kept after basic classification")
                except Exception as e2:
                    print(f"❌ Fallback classification also failed: {e2}")
                    print("📄 All downloaded files have been kept without classification")

        else:
            print("\n❌ No PDF files were downloaded.")
            print("   This could be because:")
            print("   - No annual report PDFs were found on the pages")
            print("   - The pages require authentication")
            print("   - The PDFs are embedded or dynamically loaded")

    except ImportError:
        print("❌ Required packages not installed. To enable PDF scraping, install:")
        print("   pip install requests beautifulsoup4 python-dotenv")

    except Exception as e:
        print(f"❌ Error during PDF scraping: {str(e)}")


def main():
    """Main function to run the power plant search interface."""
    print("🏭 Power Plant Annual Report Search Engine")
    print("==========================================")
    print()
    print("This tool searches for annual reports for power plants.")
    print("It will first search directly for the power plant's annual reports,")
    print("and if not found, will search for the holding company's reports.")
    print()
    print("🤖 NEW: Comprehensive Automated Mode Available!")
    print("   - Fully automated (no prompts)")
    print("   - Proper fallback: ScraperAPI → Playwright → Selenium → Direct")
    print("   - Auto-classification with OpenAI")
    print("   - Auto-deletion of non-consolidated reports")
    print()
    
    while True:
        try:
            # Get power plant name from user
            plant_name = input("Enter power plant name (or 'quit' to exit): ").strip()

            if plant_name.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break

            if not plant_name:
                print("❌ Please enter a valid power plant name.")
                continue

            # Ask user for mode selection
            print(f"\n🔍 Choose search mode for: {plant_name}")
            print("1. 🔍 Standard Mode (with user prompts)")
            print("2. 🤖 Comprehensive Automated Mode (fully automated)")

            mode_choice = input("Enter choice (1 or 2): ").strip()

            if mode_choice == "2":
                # Use comprehensive automated scraper
                print(f"\n🤖 Using Comprehensive Automated Mode for: {plant_name}")

                # Check for OpenAI API key
                if not os.getenv("OPENAI_API_KEY"):
                    print("❌ Error: OPENAI_API_KEY required for automated mode")
                    print("Please set your OpenAI API key to use this mode.")
                    continue

                try:
                    comprehensive_scraper = ComprehensiveAutomatedScraper(download_dir="./downloads")
                    results = comprehensive_scraper.run_comprehensive_automation(plant_name)

                    if results["success"]:
                        print(f"\n🎉 Automated process completed successfully!")
                    else:
                        print(f"\n❌ Automated process failed: {results.get('error', 'Unknown error')}")

                except Exception as e:
                    error_msg = str(e)
                    print(f"\n❌ Unexpected error: {error_msg}")

                    # Handle specific Google Generative AI client errors
                    if "proxies" in error_msg or "unexpected keyword argument" in error_msg:
                        print("⚠️ This appears to be a Google Generative AI client version compatibility issue.")
                        print("⚠️ Try updating your google-genai package:")
                        print("   pip install --upgrade google-genai")
                        print("⚠️ Or check your GEMINI_API_KEY configuration.")

                # Continue to next iteration
                print("\n" + "-"*50)
                continue_search = input("Process another power plant? (y/n): ").strip().lower()
                if continue_search not in ['y', 'yes']:
                    print("👋 Goodbye!")
                    break
                continue
            
            print(f"\n🔍 Searching for annual reports for: {plant_name}")
            print("⏳ This may take a few moments...")
            
            # Initialize state
            initial_state = initialize_power_plant_state(plant_name)
            
            # Run the power plant search graph
            try:
                final_state = power_plant_graph.invoke(initial_state)

                # Validate final_state structure
                if final_state is None:
                    print("\n❌ Error: Search graph returned None")
                    raise ValueError("Search graph returned None")
                
                # Check if final_state is None or missing essential components
                if not final_state or not final_state.get("messages"):
                    print("\n❌ Error: Search returned incomplete results.")
                    print("This could be because:")
                    print("   - No relevant information was found for this power plant")
                    print("   - The search API returned limited or no results")
                    print("   - There was an issue with the search process")
                    use_direct_search = True
                else:
                    # Print search results if we have a valid final state
                    # The function returns True if there's a mismatch between requested plant and results
                    result_mismatch = print_search_results(final_state)
                    
                    # If there's a mismatch, suggest direct search
                    if result_mismatch:
                        use_direct_search_response = input("\n🤖 Would you like to try direct PDF search instead? (y/n): ").strip().lower()
                        use_direct_search = use_direct_search_response in ['y', 'yes']
                    else:
                        use_direct_search = False
                        
                        # If results look good, ask if user wants to scrape PDFs
                        if final_state.get("sources_gathered"):
                            scrape_pdfs = input("\n🤖 Would you like to scrape PDFs from these sources? (y/n): ").strip().lower()
                            if scrape_pdfs in ['y', 'yes']:
                                scrape_annual_report_pdfs(final_state.get("sources_gathered", []), plant_name, final_state)
                
                # If we need to use direct search (due to error, mismatch, or user choice)
                if use_direct_search:
                    print("\n🔍 Searching for potential annual report sources...")

                    # Initialize the scraper for searching (not downloading yet)
                    scraper = ScraperAPIPDFScraper(download_dir="./downloads")

                    # Generic search for potential sources
                    print(f"\n� Searching for generic annual report sources for: {plant_name}")
                    print("� Using generic search patterns (no hardcoded companies)")

                    # Ask for permission before downloading
                    download_permission = input("\n🤖 Would you like to search and download PDFs from these sources? (y/n): ").strip().lower()

                    if download_permission in ['y', 'yes']:
                        print("\n⬇️  Starting PDF download process...")

                        # Create a directory for downloads
                        plant_dir = os.path.join("./downloads", plant_name.replace(' ', '_'))
                        os.makedirs(plant_dir, exist_ok=True)

                        downloaded_files = []

                        # Generic search with the plant name (no hardcoded companies)
                        print(f"\n🔍 Generic search for {plant_name} annual reports...")
                        current_year = 2024
                        from_year = current_year - 4
                        to_year = current_year

                        plant_files = scraper.run_downloader(plant_name, from_year, to_year)
                        if plant_files:
                            downloaded_files.extend(plant_files)

                        if downloaded_files:
                            # Remove duplicates
                            unique_files = list(set(downloaded_files))
                            print(f"\n✅ Successfully downloaded {len(unique_files)} PDF(s):")
                            for file_path in unique_files:
                                print(f"   📄 {file_path}")
                            print(f"\n📁 Files saved in: ./downloads/{plant_name.replace(' ', '_')}/")
                        else:
                            print("\n❌ No PDF files were found through direct search.")
                            print("Try a different power plant name or check your ScraperAPI key.")
                    else:
                        print("\n⏭️  Skipping PDF download. Search completed.")
                
            except Exception as e:
                error_msg = str(e)
                print(f"\n❌ Error during search: {error_msg}")

                # Provide more specific error messages
                if "'NoneType' object is not subscriptable" in error_msg:
                    print("This error suggests the search API returned incomplete data.")
                    print("This can happen when:")
                    print("   - The search query is too vague or doesn't match any results")
                    print("   - The Google Search API has temporary issues")
                    print("   - The power plant name is not well-known or documented online")
                elif "'NoneType' object is not iterable" in error_msg:
                    print("This error suggests the search API returned no grounding data.")
                    print("This can happen when no relevant sources are found.")
                else:
                    print("Please check your API keys and internet connection.")
                
                # Offer direct PDF search as a fallback option
                print("\n🔍 Main search failed. Would you like to try direct PDF search as fallback?")

                fallback_permission = input("🤖 Search for PDFs directly? (y/n): ").strip().lower()

                if fallback_permission in ['y', 'yes']:
                    try:
                        print("\n⬇️  Starting direct PDF search...")

                        # Create a directory for downloads
                        plant_dir = os.path.join("./downloads", plant_name.replace(' ', '_'))
                        os.makedirs(plant_dir, exist_ok=True)

                        # Initialize the scraper
                        scraper = ScraperAPIPDFScraper(download_dir="./downloads")

                        downloaded_files = []

                        # Generic search with the plant name (no hardcoded companies)
                        print(f"\n🔍 Generic fallback search for {plant_name} annual reports...")
                        current_year = 2024
                        from_year = current_year - 4
                        to_year = current_year

                        plant_files = scraper.run_downloader(plant_name, from_year, to_year)
                        if plant_files:
                            downloaded_files.extend(plant_files)

                        if downloaded_files:
                            # Remove duplicates
                            unique_files = list(set(downloaded_files))
                            print(f"\n✅ Successfully downloaded {len(unique_files)} PDF(s):")
                            for file_path in unique_files:
                                print(f"   📄 {file_path}")
                            print(f"\n📁 Files saved in: ./downloads/{plant_name.replace(' ', '_')}/")
                        else:
                            print("\n❌ No PDF files were found through direct search.")
                            print("Try a different power plant name or check your ScraperAPI key.")
                    except Exception as fallback_error:
                        print(f"\n❌ Fallback search also failed: {str(fallback_error)}")
                else:
                    print("\n⏭️  Skipping direct PDF search.")
                
                continue
            
            # Ask if user wants to search for another plant
            print("\n" + "-"*50)
            continue_search = input("Search for another power plant? (y/n): ").strip().lower()
            if continue_search not in ['y', 'yes']:
                print("👋 Goodbye!")
                break
                
        except KeyboardInterrupt:
            print("\n\n👋 Search interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Unexpected error: {str(e)}")
            continue


if __name__ == "__main__":
    # Check for required environment variables
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ Error: GEMINI_API_KEY environment variable is not set.")
        print("Please set your Gemini API key before running this script.")
        print("Example: export GEMINI_API_KEY='your-api-key-here'")
        sys.exit(1)
    
    # Check for ScraperAPI key (warn but don't exit)
    if not os.getenv("SCRAPER_API_KEY"):
        print("⚠️  Warning: SCRAPER_API_KEY environment variable is not set.")
        print("PDF scraping functionality will be limited without a ScraperAPI key.")
        print("To enable full PDF scraping, set your ScraperAPI key:")
        print("Example: export SCRAPER_API_KEY='your-api-key-here'")
        print()
    
    main()
