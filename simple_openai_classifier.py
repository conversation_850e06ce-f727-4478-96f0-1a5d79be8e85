#!/usr/bin/env python3
"""
Simple OpenAI PDF Classifier - Isolated from problematic imports

This module provides OpenAI-based PDF classification without any imports
that might interfere with the OpenAI client.
"""

import os
import json
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables FIRST
load_dotenv()

# Import OpenAI IMMEDIATELY after dotenv, before anything else
from openai import OpenAI

# Initialize OpenAI client immediately
try:
    openai_client = OpenAI()
    print("✅ OpenAI client initialized successfully in simple classifier")
except Exception as e:
    print(f"❌ OpenAI client initialization failed: {e}")
    openai_client = None


def extract_text_simple(pdf_path: str, max_pages: int = 5) -> str:
    """Extract text from PDF using simple methods only."""
    try:
        # Try PyPDF2 first (simple and reliable)
        import PyPDF2
        text = ""
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            num_pages = min(len(pdf_reader.pages), max_pages)
            
            for page_num in range(num_pages):
                page = pdf_reader.pages[page_num]
                text += page.extract_text() + "\n"
        
        return text.strip()
        
    except Exception as e:
        print(f"⚠️ PyPDF2 extraction failed: {e}")
        
        try:
            # Fallback to pdfplumber
            import pdfplumber
            text = ""
            with pdfplumber.open(pdf_path) as pdf:
                num_pages = min(len(pdf.pages), max_pages)
                
                for page_num in range(num_pages):
                    page = pdf.pages[page_num]
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
            
            return text.strip()
            
        except Exception as e2:
            print(f"⚠️ pdfplumber extraction failed: {e2}")
            return ""


def classify_pdf_with_openai(text: str, plant_name: str, model: str = "gpt-4o") -> bool:
    """Classify PDF using OpenAI without any problematic imports.
    
    Args:
        text: Extracted text from PDF
        plant_name: Name of the power plant
        model: OpenAI model to use
        
    Returns:
        True if PDF should be kept, False if it should be deleted
    """
    if not openai_client:
        print("⚠️ OpenAI client not available")
        return True  # Conservative: keep file if can't classify
    
    system_prompt = (
        "You are a document classification assistant. Your task is to check whether a given document "
        "is an annual report and whether it belongs to the specified power plant. Additionally, determine "
        "the type of financial reports included (standalone, consolidated, or both)."
    )
    
    user_prompt = f"""
Please analyze this document text and determine:

1. Is this an annual report? (yes/no)
2. Does it belong to "{plant_name}" or its parent/subsidiary companies? (yes/no)
3. What type of financial statements does it contain?
   - Standalone financial statements only
   - Consolidated financial statements only
   - Both standalone and consolidated financial statements
   - Neither/unclear

DECISION RULES:
- KEEP if: Contains standalone reports (with or without consolidated)
- KEEP if: Contains both standalone and consolidated in the same document
- DELETE if: Contains only consolidated financial statements
- KEEP if: Document type is unclear but belongs to the company

Document text (first 3000 characters):
{text[:3000]}

Please respond in JSON format:
{{
    "is_annual_report": true/false,
    "belongs_to_company": true/false,
    "financial_statement_type": "standalone_only" | "consolidated_only" | "both" | "unclear",
    "decision": "keep" | "delete",
    "reasoning": "Brief explanation of the decision"
}}
"""
    
    try:
        response = openai_client.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            temperature=0,
            max_tokens=500
        )
        
        result_text = response.choices[0].message.content.strip()
        
        # Parse JSON response
        try:
            result = json.loads(result_text)
            
            decision = result.get("decision", "keep").lower()
            reasoning = result.get("reasoning", "No reasoning provided")
            
            if decision == "keep":
                print(f"✅ KEEP: {reasoning}")
                return True
            else:
                print(f"🗑️ DELETE: {reasoning}")
                return False
                
        except json.JSONDecodeError:
            print(f"⚠️ Could not parse OpenAI response as JSON: {result_text}")
            print("✅ KEEP: Keeping file due to parsing error (conservative approach)")
            return True
            
    except Exception as e:
        print(f"❌ OpenAI classification error: {e}")
        print("✅ KEEP: Keeping file due to classification error (conservative approach)")
        return True


def classify_pdf_file(pdf_path: str, plant_name: str) -> bool:
    """Classify a single PDF file.
    
    Args:
        pdf_path: Path to the PDF file
        plant_name: Name of the power plant
        
    Returns:
        True if PDF should be kept, False if it should be deleted
    """
    print(f"\n📄 Processing: {Path(pdf_path).name}")
    
    # Extract text
    text = extract_text_simple(pdf_path)
    
    if not text:
        print("⚠️ Could not extract text, keeping file as fallback")
        return True
    
    # Classify with OpenAI
    return classify_pdf_with_openai(text, plant_name)


def process_pdf_list_simple(pdf_list: list, plant_name: str) -> list:
    """Process a list of PDFs with simple OpenAI classification.
    
    Args:
        pdf_list: List of PDF file paths
        plant_name: Name of the power plant
        
    Returns:
        List of PDF files that should be kept
    """
    print(f"\n🔍 Starting simple OpenAI PDF classification for {plant_name}")
    print(f"📋 Found {len(pdf_list)} PDFs to process")
    print("📝 Classification Rules:")
    print("   ✅ KEEP: Standalone reports")
    print("   ✅ KEEP: PDFs with both standalone + consolidated")
    print("   🗑️ DELETE: Consolidated-only reports")
    print("-" * 60)
    
    kept_files = []
    deleted_files = []
    
    for pdf_path in pdf_list:
        try:
            should_keep = classify_pdf_file(pdf_path, plant_name)
            
            if should_keep:
                kept_files.append(pdf_path)
            else:
                deleted_files.append(pdf_path)
                # Actually delete the file
                try:
                    os.remove(pdf_path)
                    print(f"🗑️ Deleted: {Path(pdf_path).name}")
                except Exception as e:
                    print(f"⚠️ Could not delete {Path(pdf_path).name}: {e}")
                    kept_files.append(pdf_path)  # Keep if can't delete
                    
        except Exception as e:
            print(f"❌ Error processing {Path(pdf_path).name}: {e}")
            print("✅ Keeping file due to processing error")
            kept_files.append(pdf_path)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 CLASSIFICATION SUMMARY:")
    print(f"✅ Kept: {len(kept_files)} files")
    print(f"🗑️ Deleted: {len(deleted_files)} files")
    
    if kept_files:
        print("\n✅ KEPT FILES:")
        for file_path in kept_files:
            print(f"   📄 {Path(file_path).name}")
    
    if deleted_files:
        print("\n🗑️ DELETED FILES:")
        for file_path in deleted_files:
            print(f"   📄 {Path(file_path).name}")
    
    print("=" * 60)
    
    return kept_files


if __name__ == "__main__":
    # Test the simple classifier
    print("🧪 Testing Simple OpenAI Classifier")
    print("=" * 50)
    
    if openai_client:
        print("✅ OpenAI client is available")
        
        # Test with a simple classification
        test_text = "This is the annual report for SEIL Energy India Limited for the year 2023-24. It contains standalone financial statements."
        result = classify_pdf_with_openai(test_text, "SEIL Energy India Limited")
        print(f"Test result: {'KEEP' if result else 'DELETE'}")
    else:
        print("❌ OpenAI client is not available")
