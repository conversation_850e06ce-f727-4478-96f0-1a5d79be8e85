from get_org_country import get_power_plant_details
from get_org_id import generate_org_uid
from get_annualreports import run_downloader,has_annual_report
from agentic_layer.clem_transition import clem_transition_financial



def orchestrate_pipeline_from_input(input_data: dict):
    """
    Orchestrator that accepts a dictionary input, extracts plant name and product,
    and triggers the corresponding pipeline if the annual report is found.
    """
    # Extract and validate input
    plant_name = input_data.get("plantName")
    domain_name = input_data.get("Product")

    if not plant_name or not domain_name:
        raise ValueError("Input must contain both 'plantName' and 'Product' fields.")

    print(f"[INFO] Orchestration started for plant: {plant_name}, product/domain: {domain_name}")
    run_downloader(plant_name, from_year=2010, to_year=2023)
    # Function to check if annual report exists


    if has_annual_report(plant_name):
        print(f"[INFO] Annual report found for {plant_name}. Triggering domain pipeline.")
        # Get organization details
        org_details = get_power_plant_details(plant_name)
        org_name = org_details.get("org_name")
        country = org_details.get("country")
        plant_name_uid = generate_org_uid(org_name,country,plant_name)
        if domain_name == "clem-transition":
                clem_transition_financial(plant_name, domain_name)
    else:
        print(f"[WARNING] No annual report found for {plant_name}. Skipping pipeline.")